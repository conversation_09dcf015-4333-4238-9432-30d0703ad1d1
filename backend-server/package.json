{"name": "industrial-geo-backend", "version": "1.0.0", "description": "Backend API server for Industrial Geography Development Platform", "main": "server.cjs", "scripts": {"start": "node server.cjs", "dev": "nodemon server.cjs", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["industrial", "geography", "api", "mysql"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "node-fetch": "^2.6.7"}, "devDependencies": {"nodemon": "^3.1.10"}}