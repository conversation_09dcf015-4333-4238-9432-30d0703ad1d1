// API服务配置
const API_BASE_URL = 'http://localhost:3001/api';

// 是否使用模拟API（当后端未启动时）
const USE_MOCK_API = false; // 设置为false以使用真实数据库连接

// 获取存储的认证令牌
const getAuthToken = () => {
  return localStorage.getItem('authToken');
};

// 设置认证令牌
const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem('authToken', token);
  } else {
    localStorage.removeItem('authToken');
  }
};

// 获取存储的用户信息
const getStoredUser = () => {
  const userStr = localStorage.getItem('currentUser');
  return userStr ? JSON.parse(userStr) : null;
};

// 设置用户信息
const setStoredUser = (user) => {
  if (user) {
    localStorage.setItem('currentUser', JSON.stringify(user));
  } else {
    localStorage.removeItem('currentUser');
  }
};

// 通用API请求函数
const apiRequest = async (endpoint, options = {}) => {
  // 直接使用真实API连接
  const url = `${API_BASE_URL}${endpoint}`;
  const token = getAuthToken();

  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  try {
    console.log(`发送API请求: ${endpoint}`, config);
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      // 如果是认证错误，清除本地存储的认证信息
      if (response.status === 401 || response.status === 403) {
        setAuthToken(null);
        setStoredUser(null);
      }
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error(`API请求失败 (${endpoint}):`, error);
    throw error;
  }
};

// 认证相关API
export const authAPI = {
  // 用户注册
  register: async (userData) => {
    const response = await apiRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    if (response.success && response.data) {
      setAuthToken(response.data.token);
      setStoredUser(response.data.user);
    }

    return response;
  },

  // 用户登录 - 支持邮箱或用户名
  login: async (credentials) => {
    // 转换为后端期望的格式
    const loginData = {
      loginIdentifier: credentials.email || credentials.username || credentials.loginIdentifier,
      password: credentials.password
    };

    const response = await apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(loginData),
    });

    if (response.success && response.data) {
      setAuthToken(response.data.token);
      setStoredUser(response.data.user);
    }

    return response;
  },

  // 获取当前用户信息
  getCurrentUser: async () => {
    return await apiRequest('/auth/me');
  },

  // 更新用户资料
  updateProfile: async (profileData) => {
    const response = await apiRequest('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });

    if (response.success && response.data) {
      setStoredUser(response.data.user);
    }

    return response;
  },

  // 登出
  logout: () => {
    setAuthToken(null);
    setStoredUser(null);
    return Promise.resolve({ success: true });
  },

  // 检查是否已登录
  isAuthenticated: () => {
    return !!getAuthToken();
  },

  // 获取当前用户（从本地存储）
  getUser: () => {
    return getStoredUser();
  }
};

// 项目相关API
export const projectAPI = {
  // 获取所有项目
  getProjects: async () => {
    return await apiRequest('/projects');
  },

  // 获取单个项目
  getProject: async (projectId) => {
    return await apiRequest(`/projects/${projectId}`);
  },

  // 创建新项目
  createProject: async (projectData) => {
    return await apiRequest('/projects', {
      method: 'POST',
      body: JSON.stringify(projectData),
    });
  },

  // 更新项目
  updateProject: async (projectId, projectData) => {
    return await apiRequest(`/projects/${projectId}`, {
      method: 'PUT',
      body: JSON.stringify(projectData),
    });
  },

  // 删除项目
  deleteProject: async (projectId) => {
    return await apiRequest(`/projects/${projectId}`, {
      method: 'DELETE',
    });
  },

  // 切换项目收藏状态
  toggleFavorite: async (projectId) => {
    return await apiRequest(`/projects/${projectId}/favorite`, {
      method: 'PATCH',
    });
  }
};

// AI分析相关API
export const aiAPI = {
  // 分析文本
  analyzeText: async (inputText) => {
    return await apiRequest('/analyze', {
      method: 'POST',
      body: JSON.stringify({ inputText }),
    });
  }
};

// 系统相关API
export const systemAPI = {
  // 健康检查
  healthCheck: async () => {
    return await apiRequest('/health');
  },

  // 数据库状态检查
  databaseStatus: async () => {
    return await apiRequest('/db-status');
  }
};

// 导出工具函数
export {
  getAuthToken,
  setAuthToken,
  getStoredUser,
  setStoredUser,
  apiRequest
};
