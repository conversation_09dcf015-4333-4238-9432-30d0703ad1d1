import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import MainPage from './pages/MainPage';
import ParkDetailPage from './pages/ParkDetailPage';
import WelcomePage from './pages/WelcomePage';
import WelcomePageSimple from './pages/WelcomePageSimple';
import LoginPage from './pages/LoginPage';
import AIAnalyzerPage from './pages/AIAnalyzerPage';
import AIProjectPage from './pages/AIProjectPage';
import SiteSelectionPage from './pages/SiteSelectionPage';
import TestPage from './pages/TestPage';
import './styles/App.css'; // 添加全局样式
import './styles/park-popup.css'; // 工业园区弹出窗口样式

function App() {
  return (
    <div className="app-container">
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<WelcomePage />} />
          <Route path="/simple" element={<WelcomePageSimple />} />
          <Route path="/test" element={<TestPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/main" element={<MainPage />} />
          <Route path="/park/:id" element={<ParkDetailPage />} />
          <Route path="/ai-analyzer" element={<AIAnalyzerPage />} />
          <Route path="/ai-project/:id" element={<AIProjectPage />} />
          <Route path="/site-selection" element={<SiteSelectionPage />} />
        </Routes>
      </BrowserRouter>
    </div>
  );
}

export default App;
