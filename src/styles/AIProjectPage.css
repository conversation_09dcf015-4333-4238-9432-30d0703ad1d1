/* ===== CSS VARIABLES - DESIGN SYSTEM ===== */
:root {
  /* Primary Colors */
  --primary-color: #4dc8ff;
  --primary-dark: #2196f3;
  --primary-light: #77d5ff;
  --secondary-color: #00ff88;
  --accent-color: #64b5f6;

  /* Background Colors */
  --bg-primary: #0f1419;
  --bg-secondary: #1a1f2e;
  --bg-tertiary: #2a2f3e;
  --bg-surface: rgba(26, 31, 46, 0.95);
  --bg-glass: rgba(26, 31, 46, 0.8);
  --bg-panel: rgba(42, 47, 62, 0.9);

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-muted: rgba(255, 255, 255, 0.7);
  --text-accent: var(--primary-color);

  /* Border & Shadow */
  --border-primary: rgba(77, 200, 255, 0.2);
  --border-secondary: rgba(255, 255, 255, 0.1);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.25);

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Global overscroll protection */
html.ai-project-active,
body.ai-project-active {
  background-color: var(--bg-primary) !important;
  overscroll-behavior: none;
}

/* ===== PAGE FOUNDATION ===== */
.ai-project-page {
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, rgba(77, 200, 255, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 255, 136, 0.04) 0%, transparent 50%),
    linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  position: relative;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--text-primary);
  overscroll-behavior: none;
  line-height: 1.6;
}

/* ===== BACKGROUND ELEMENTS ===== */
.ai-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-primary);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(90deg, transparent 98%, rgba(77, 200, 255, 0.1) 98%),
    linear-gradient(0deg, transparent 98%, rgba(77, 200, 255, 0.1) 98%);
  background-size: 80px 80px;
  opacity: 0.3;
  animation: gridPulse 8s ease-in-out infinite;
}

@keyframes gridPulse {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.4; }
}

/* ===== HEADER DESIGN ===== */
.ai-header {
  position: relative;
  z-index: 100;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-secondary);
  box-shadow: var(--shadow-md);
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-lg) var(--spacing-xl);
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  align-items: center;
  gap: var(--spacing-lg);
}

.header-left {
  display: flex;
  justify-content: flex-start;
}

.header-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.header-right {
  display: flex;
  justify-content: flex-end;
}

/* Back Button */
.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: var(--bg-panel);
  border-color: var(--border-primary);
  color: var(--text-primary);
  transform: translateX(-2px);
  box-shadow: var(--shadow-md);
}

.back-button svg {
  transition: transform var(--transition-normal);
}

.back-button:hover svg {
  transform: translateX(-2px);
}

/* Project Title */
.project-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.project-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 13px;
  color: var(--text-muted);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--secondary-color);
  box-shadow: 0 0 8px rgba(0, 255, 136, 0.4);
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

/* Language Selector */
.language-selector {
  position: relative;
}

.language-trigger {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  min-width: 120px;
}

.language-trigger:hover {
  background: var(--bg-panel);
  border-color: var(--border-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
}

.language-flag {
  font-size: 16px;
}

.language-name {
  flex: 1;
}

.dropdown-icon {
  transition: transform var(--transition-normal);
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

.language-menu {
  position: absolute;
  top: calc(100% + var(--spacing-sm));
  right: 0;
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  min-width: 180px;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
  text-align: left;
}

.language-option:hover {
  background: var(--bg-panel);
  color: var(--text-primary);
}

.language-option.active {
  background: rgba(77, 200, 255, 0.1);
  color: var(--primary-color);
}

.check-icon {
  color: var(--primary-color);
}

/* ===== MAIN CONTENT LAYOUT ===== */
.ai-main {
  position: relative;
  z-index: 10;
  padding: var(--spacing-2xl) 0;
  min-height: calc(100vh - 120px);
}

.ai-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
}

.content-layout {
  display: grid;
  gap: var(--spacing-xl);
  align-items: start;
  transition: all var(--transition-slow);
}

.content-layout.dual-panel {
  grid-template-columns: 1fr 1fr;
}

.content-layout.results-focused {
  grid-template-columns: 1fr;
  max-width: 900px;
  margin: 0 auto;
}

.content-layout.results-focused .input-panel.hidden {
  display: none;
}

/* ===== PANEL STYLES ===== */
.input-panel,
.results-panel {
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.input-panel:hover,
.results-panel:hover {
  border-color: var(--border-primary);
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.panel-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-secondary);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.header-info h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-info p {
  font-size: 14px;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.4;
}

/* Input Panel Meta */
.input-meta,
.results-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 12px;
  color: var(--text-muted);
}

.char-count,
.results-count {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(77, 200, 255, 0.1);
  border-radius: var(--radius-sm);
  font-weight: 600;
  color: var(--primary-color);
  min-width: 24px;
  text-align: center;
}

.view-toggle-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--bg-panel);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--text-muted);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.view-toggle-button:hover {
  background: var(--bg-surface);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

/* ===== INPUT CONTENT ===== */
.input-content {
  padding: var(--spacing-xl);
}

.input-textarea {
  width: 100%;
  min-height: 360px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.6;
  resize: vertical;
  transition: all var(--transition-normal);
  font-family: inherit;
  font-weight: 400;
}

.input-textarea:focus {
  outline: none;
  border-color: var(--border-primary);
  box-shadow: 0 0 0 3px rgba(77, 200, 255, 0.1);
  background: rgba(255, 255, 255, 0.04);
}

.input-textarea::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

.quick-inputs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  margin-top: var(--spacing-md);
}

.quick-input-chip {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-muted);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.quick-input-chip:hover {
  background: rgba(77, 200, 255, 0.1);
  border-color: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* ===== INPUT ACTIONS ===== */
.input-actions {
  padding: var(--spacing-xl);
  border-top: 1px solid var(--border-secondary);
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.analyze-button.primary {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg) var(--spacing-xl);
  color: var(--bg-primary);
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  min-height: 56px;
}

.analyze-button.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.analyze-button.primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

.analyze-button.primary:hover:not(:disabled)::before {
  left: 100%;
}

.analyze-button.primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.clear-button.secondary {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--bg-panel);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-muted);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.clear-button.secondary:hover {
  background: var(--bg-surface);
  border-color: var(--border-primary);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(15, 25, 40, 0.2);
  border-top: 2px solid var(--bg-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== RESULTS CONTENT ===== */
.results-content {
  padding: var(--spacing-xl);
}

.empty-state,
.error-state {
  text-align: center;
  padding: var(--spacing-2xl) var(--spacing-xl);
  color: var(--text-muted);
}

.empty-icon,
.error-icon {
  margin-bottom: var(--spacing-lg);
  color: var(--border-primary);
  opacity: 0.6;
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

.empty-state h3,
.error-state h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-sm) 0;
}

.empty-state p,
.error-state p {
  font-size: 14px;
  color: var(--text-muted);
  margin: 0 0 var(--spacing-lg) 0;
  line-height: 1.5;
}

.error-state {
  background: rgba(255, 71, 87, 0.05);
  border: 1px solid rgba(255, 71, 87, 0.1);
  border-radius: var(--radius-md);
  margin: var(--spacing-lg);
}

.error-state .error-icon {
  color: #ff4757;
}

.error-state h4 {
  color: #ff4757;
}

.retry-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(255, 71, 87, 0.1);
  border: 1px solid rgba(255, 71, 87, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: #ff4757;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  margin: 0 auto;
}

.retry-button:hover {
  background: rgba(255, 71, 87, 0.15);
  border-color: rgba(255, 71, 87, 0.3);
  transform: translateY(-1px);
}

/* Analysis Results */
.analysis-results {
  background: transparent;
}

.summary-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: rgba(77, 200, 255, 0.05);
  border: 1px solid rgba(77, 200, 255, 0.1);
  border-radius: var(--radius-md);
}

.summary-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 var(--spacing-sm) 0;
}

.summary-text {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
}

.results-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.results-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-panel);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-normal);
  width: 36px;
  height: 36px;
}

.export-button:hover {
  background: var(--bg-surface);
  border-color: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.edit-hint {
  font-size: 11px;
  color: var(--text-muted);
  font-style: italic;
}

/* ===== RESULTS GRID ===== */
.results-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.result-category {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.result-category:hover {
  border-color: var(--border-primary);
  background: rgba(255, 255, 255, 0.03);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.category-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-secondary);
  background: rgba(77, 200, 255, 0.02);
}

.category-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.category-info h4 {
  color: var(--primary-color);
  margin: 0;
  font-size: 0.95rem;
  font-weight: 600;
}

.item-count {
  background: rgba(77, 200, 255, 0.1);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 11px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.add-item-button {
  background: rgba(77, 200, 255, 0.08);
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  opacity: 0.7;
}

.add-item-button:hover {
  background: rgba(77, 200, 255, 0.15);
  border-color: rgba(77, 200, 255, 0.3);
  opacity: 1;
  transform: scale(1.05);
}

.result-items {
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  min-height: 60px;
  align-items: flex-start;
}

.result-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(77, 200, 255, 0.06);
  border: 1px solid rgba(77, 200, 255, 0.15);
  border-radius: var(--radius-xl);
  padding: 2px;
  transition: all var(--transition-fast);
  animation: itemFadeIn 0.3s ease-out;
}

@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.result-item:hover {
  background: rgba(77, 200, 255, 0.1);
  border-color: rgba(77, 200, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(77, 200, 255, 0.1);
}

.result-tag {
  background: transparent;
  border: none;
  color: var(--primary-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 12px;
  font-weight: 500;
  cursor: text;
  outline: none;
  min-width: 20px;
  text-align: center;
  transition: all var(--transition-fast);
}

.result-tag:hover {
  background: rgba(77, 200, 255, 0.08);
}

.result-tag:focus {
  background: rgba(77, 200, 255, 0.12);
  box-shadow: 0 0 0 2px rgba(77, 200, 255, 0.2);
}

.delete-item-button {
  background: rgba(255, 71, 87, 0.1);
  border: 1px solid rgba(255, 71, 87, 0.2);
  border-radius: 50%;
  padding: var(--spacing-xs);
  color: rgba(255, 71, 87, 0.8);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  width: 20px;
  height: 20px;
}

.result-item:hover .delete-item-button {
  opacity: 1;
  transform: scale(1);
}

.delete-item-button:hover {
  background: rgba(255, 71, 87, 0.15);
  border-color: rgba(255, 71, 87, 0.3);
  transform: scale(1.1);
}

/* ===== SITE SELECTION BUTTON ===== */
.site-selection-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--spacing-2xl);
  padding: 0 var(--spacing-xl) var(--spacing-2xl) var(--spacing-xl);
  animation: fadeInUp 0.8s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.site-selection-button {
  position: relative;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 2px solid var(--primary-color);
  border-radius: var(--radius-lg);
  padding: 0;
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  min-width: 280px;
  height: 70px;
}

.site-selection-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.site-selection-button:hover {
  transform: translateY(-3px) scale(1.02);
  border-color: var(--primary-light);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  box-shadow:
    0 15px 35px rgba(77, 200, 255, 0.3),
    0 8px 20px rgba(77, 200, 255, 0.2);
}

.site-selection-button:hover::before {
  left: 100%;
}

.site-selection-button:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: var(--shadow-md);
}

.button-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  height: 100%;
  color: var(--bg-primary);
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  transition: all var(--transition-normal);
}

.site-selection-button:hover .button-content {
  color: var(--bg-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.button-content svg {
  transition: all var(--transition-normal);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.site-selection-button:hover .button-content svg {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .content-layout.dual-panel {
    gap: var(--spacing-lg);
  }

  .site-selection-section {
    margin-top: var(--spacing-xl);
    padding: 0 var(--spacing-lg) var(--spacing-xl) var(--spacing-lg);
  }

  .site-selection-button {
    min-width: 260px;
    height: 65px;
  }

  .button-content {
    font-size: 15px;
  }
}

@media (max-width: 1024px) {
  .content-layout.dual-panel {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .content-layout.results-focused {
    max-width: 100%;
  }

  .header-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    text-align: center;
  }

  .header-left,
  .header-right {
    justify-content: center;
  }

  .project-title {
    font-size: 1.6rem;
  }

  .language-menu {
    right: auto;
    left: 50%;
    transform: translateX(-50%);
  }

  .edit-hint {
    display: none;
  }
}

@media (max-width: 768px) {
  .ai-container {
    padding: 0 var(--spacing-md);
  }

  .header-container {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .back-button,
  .language-trigger {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 13px;
  }

  .project-title {
    font-size: 1.4rem;
  }

  .language-menu {
    min-width: 200px;
  }

  .input-panel,
  .results-panel {
    border-radius: var(--radius-md);
  }

  .panel-header {
    padding: var(--spacing-lg) var(--spacing-lg);
  }

  .header-info h2 {
    font-size: 1.2rem;
  }

  .input-content {
    padding: var(--spacing-lg);
  }

  .input-textarea {
    min-height: 280px;
    padding: var(--spacing-md);
  }

  .quick-inputs {
    gap: var(--spacing-xs);
  }

  .quick-input-chip {
    font-size: 10px;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .input-actions {
    flex-direction: column;
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
  }

  .analyze-button.primary,
  .clear-button.secondary {
    width: 100%;
    justify-content: center;
  }

  .results-content {
    padding: var(--spacing-lg);
  }

  .results-grid {
    gap: var(--spacing-md);
  }

  .category-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .result-items {
    padding: var(--spacing-md) var(--spacing-lg);
    gap: var(--spacing-xs);
  }

  .add-item-button {
    width: 24px;
    height: 24px;
  }

  .result-item {
    gap: 2px;
  }

  .result-tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 11px;
  }

  .delete-item-button {
    width: 18px;
    height: 18px;
  }

  .site-selection-section {
    margin-top: var(--spacing-lg);
    padding: 0 var(--spacing-md) var(--spacing-lg) var(--spacing-md);
  }

  .site-selection-button {
    min-width: 240px;
    height: 60px;
  }

  .button-content {
    font-size: 14px;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .ai-container {
    padding: 0 var(--spacing-sm);
  }

  .header-container {
    padding: var(--spacing-md) var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .project-title {
    font-size: 1.2rem;
  }

  .content-layout.dual-panel {
    gap: var(--spacing-md);
  }

  .panel-header {
    padding: var(--spacing-md);
  }

  .header-info h2 {
    font-size: 1.1rem;
  }

  .input-content {
    padding: var(--spacing-md);
  }

  .input-textarea {
    min-height: 240px;
    padding: var(--spacing-sm);
    font-size: 13px;
  }

  .input-actions {
    padding: var(--spacing-md);
  }

  .results-content {
    padding: var(--spacing-md);
  }

  .content-layout.results-focused {
    max-width: 100%;
  }

  .result-item {
    padding: 1px;
  }

  .result-tag {
    padding: 3px 6px;
    font-size: 10px;
  }

  .delete-item-button {
    width: 16px;
    height: 16px;
  }

  .add-item-button {
    width: 20px;
    height: 20px;
    padding: 3px;
  }

  .results-actions {
    gap: var(--spacing-sm);
  }

  .export-button {
    width: 32px;
    height: 32px;
    padding: var(--spacing-xs);
  }

  .site-selection-section {
    margin-top: var(--spacing-md);
    padding: 0 var(--spacing-sm) var(--spacing-md) var(--spacing-sm);
  }

  .site-selection-button {
    min-width: 220px;
    height: 55px;
  }

  .button-content {
    font-size: 13px;
    gap: var(--spacing-sm);
    letter-spacing: 0.3px;
  }

  .button-content svg {
    width: 20px;
    height: 20px;
  }
}

/* 站点选择模态框样式 */
.site-selection-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease;
}

.site-selection-modal {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  max-width: 95vw;
  max-height: 90vh;
  width: 1200px;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.modal-header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

.view-toggle-controls {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 0.25rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.view-toggle-controls .view-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-toggle-controls .view-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #4dc8ff;
}

.view-toggle-controls .view-toggle-btn.active {
  background: rgba(77, 200, 255, 0.2);
  color: #4dc8ff;
  border: 1px solid rgba(77, 200, 255, 0.3);
}

.modal-header h2 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(135deg, #fff 0%, #4dc8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close-btn {
  background: none;
  border: none;
  color: #ffffff;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.modal-content {
  padding: 2rem;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

.prospecting-results h3 {
  color: #ffffff;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #fff 0%, #4dc8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.results-subtitle {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
  font-size: 0.9rem;
}

.parcels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.parcel-card {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.parcel-card:hover {
  transform: translateY(-2px);
  border-color: rgba(77, 200, 255, 0.3);
  box-shadow: 0 8px 32px rgba(77, 200, 255, 0.1);
}

.parcel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
}

.parcel-header h4 {
  color: #4dc8ff;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.parcel-rank {
  background: linear-gradient(135deg, #4dc8ff 0%, #00d4ff 100%);
  color: #0a0f1c;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 700;
}

.parcel-info {
  margin-bottom: 1rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.info-row .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-row .value {
  color: #ffffff;
  font-weight: 600;
}

.cost-breakdown {
  margin-bottom: 1.5rem;
}

.cost-breakdown h5 {
  color: #4dc8ff;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.breakdown-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  padding: 0.25rem 0;
}

.breakdown-item span:first-child {
  color: rgba(255, 255, 255, 0.6);
}

.breakdown-item span:last-child {
  color: #ffffff;
  font-weight: 500;
}

.select-parcel-btn {
  width: 100%;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.15) 0%, rgba(77, 200, 255, 0.25) 100%);
  border: 1px solid rgba(77, 200, 255, 0.3);
  color: #4dc8ff;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.select-parcel-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.25) 0%, rgba(77, 200, 255, 0.35) 100%);
  border-color: rgba(77, 200, 255, 0.5);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(77, 200, 255, 0.2);
}

.select-parcel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.financial-analysis-results {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.financial-analysis-results h3 {
  color: #ffffff;
  font-size: 1.3rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #fff 0%, #00ff88 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.financial-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  backdrop-filter: blur(10px);
}

.summary-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.summary-item .value {
  color: #00ff88;
  font-size: 1.2rem;
  font-weight: 700;
}

/* 地图视图容器样式 */
.map-view-container {
  padding: 0;
}

.map-view-container h3 {
  color: #ffffff;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  padding: 0 2rem;
  background: linear-gradient(135deg, #fff 0%, #4dc8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.map-view-container .results-subtitle {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
  font-size: 0.9rem;
  padding: 0 2rem;
}

.modal-map {
  border-radius: 0;
  border: none;
  box-shadow: none;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
