#root {
  max-width: 100%;
  margin: 0;
  padding: 0;
  text-align: center;
  width: 100%;
  height: 100%;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}


body {
  margin: 0;
  padding: 0;
  background-color: #f9f9f9;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  /* 移除 overflow: hidden，允许页面滚动 */
}

/* Override body background for AI Project Page to prevent light background during overscroll */
body.ai-project-active {
  background-color: #050a12 !important;
}

.app-container {
  min-height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
  /* 确保容器可以滚动 */
  overflow-y: auto;
}

html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  /* 隐藏滚动条但保持滚动功能 */
  overflow-x: hidden;
}

/* 隐藏滚动条 - Webkit浏览器 (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
}

/* 隐藏滚动条 - Firefox */
html {
  scrollbar-width: none;
}

/* 确保所有容器都隐藏滚动条 */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

*::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}
