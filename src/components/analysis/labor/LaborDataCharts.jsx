import React, { useState, useEffect } from 'react';
import { Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import translations from '../../../utils/translations'; // 确保正确导入

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const LaborDataCharts = ({ parkName, language }) => {
  const [laborData, setLaborData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('');
  const [availableRegions, setAvailableRegions] = useState([]);
  
  // 添加错误处理
  const t = (() => {
    try {
      return translations[language] || translations['en'];
    } catch (err) {
      console.error('Translation object access error:', err);
      return { loading: 'Loading...', dataLoadError: 'Cannot load labor data' };
    }
  })();

  useEffect(() => {
    fetch('/data/thailand_central_labor_data_2024Q4.json')
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Loaded labor data:', data);
        setLaborData(data);

        const regions = Object.keys(data);
        const sortedRegions = regions.sort((a, b) => {
          if (a === 'Regional Total') return -1;
          if (b === 'Regional Total') return 1;
          if (a === 'Prachin Buri') return -1;
          if (b === 'Prachin Buri') return 1;
          return a.localeCompare(b);
        });
        setAvailableRegions(sortedRegions);

        if (parkName === '304 Industrial Park (Prachinburi）' && sortedRegions.includes('Prachin Buri')) {
          setSelectedRegion('Prachin Buri');
        } else if (sortedRegions.includes('Regional Total')) {
          setSelectedRegion('Regional Total');
        } else if (sortedRegions.length > 0) {
          setSelectedRegion(sortedRegions[0]);
        }
        setLoading(false);
      })
      .catch((error) => {
        console.error('Error loading labor data:', error);
        setLaborData(null);
        setLoading(false);
      });
  }, [parkName]);

  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>{t.loading || '加载中...'}</p>
      </div>
    );
  }

  if (!laborData || !selectedRegion) {
    return (
      <div className="error-message">
        <p>{t.dataLoadError || '无法加载劳动力数据'}</p>
      </div>
    );
  }

  const regionData = laborData[selectedRegion];

  const prepareEmploymentByIndustryData = () => {
    if (!regionData || !regionData.Table4 || !regionData.Table4.Total) return null;
    const industries = [
      'Manufacturing',
      'Wholesale and Retail Trade',
      'Accommodation and Food Services',
      'Transportation and Warehousing',
      'Construction',
      'Agriculture, Forestry, and Fishing',
    ];
    const data = industries.map((industry) => {
      const value = regionData.Table4.Total[industry];
      return value === 'n.a.' ? 0 : Math.round(parseFloat(value) || 0);
    });
    return {
      labels: industries.map((industry) => t[industry.toLowerCase().replace(/\s+/g, '_')] || industry),
      datasets: [
        {
          label: t.employedPersons || '就业人数',
          data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  const prepareEmploymentByOccupationData = () => {
    if (!regionData || !regionData.Table3 || !regionData.Table3.Total) return null;
    const occupations = [
      'Managers, Senior Officials, Legislators',
      'Professionals ',
      'Technicians and Associate Professionals',
      'Clerks',
      'Service Workers and Salespersons',
      'Craftsmen and Related Workers',
      'Plant and Machine Operators and Assemblers',
      'general labor',
    ];
    const data = occupations.map((occupation) => {
      const value = regionData.Table3.Total[occupation];
      return value === 'n.a.' ? 0 : Math.round(parseFloat(value) || 0);
    });
    return {
      labels: occupations.map((occupation) => t[occupation.toLowerCase().replace(/\s+/g, '_')] || occupation),
      datasets: [
        {
          label: t.employedPersons || '就业人数',
          data,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  const prepareLaborForceParticipationData = () => {
    if (!regionData || !regionData.Table1 || !regionData.Table1.Total) return null;
    const laborForce =
      regionData.Table1.Total['Total （Total Labor Force， employed+unemployed+seasonally unemployed）'];
    const notInLaborForce = regionData.Table1.Total['Total (Persons Not in the Labor Force)'];
    return {
      labels: [t.inLaborForce || '劳动力人口', t.notInLaborForce || '非劳动力人口'],
      datasets: [
        {
          data: [laborForce, notInLaborForce].map((value) => Math.round(parseFloat(value) || 0)),
          backgroundColor: ['rgba(54, 162, 235, 0.6)', 'rgba(255, 99, 132, 0.6)'],
          borderColor: ['rgba(54, 162, 235, 1)', 'rgba(255, 99, 132, 1)'],
          borderWidth: 1,
        },
      ],
    };
  };

  // 删除 prepareDetailedLaborForceData 函数（约第160行开始）
  
  // 新增就业人数数据准备函数
  const prepareEmploymentData = () => {
    if (!regionData || !regionData.Table1 || !regionData.Table1.Total) return null;
    
    return {
      labels: [t.employedPersons || '就业人数'],
      datasets: [
        {
          label: t.inLaborForce || 'In Labor Force',
          data: [Math.round(parseFloat(regionData.Table1.Total['Employed Persons ']) || 0)],
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        }
      ]
    };
  };

  // 新增其他劳动力类别数据准备函数
  const prepareOtherLaborForceData = () => {
    if (!regionData || !regionData.Table1 || !regionData.Table1.Total) return null;
    
    const laborForceCategories = [
      { key: 'Unemployed Persons', label: t.unemployedPersons || 'Unemployed Persons' },
      { key: 'Seasonally Unemployed', label: t.seasonallyUnemployed || 'Seasonally Unemployed' }
    ];
    
    const nonLaborForceCategories = [
      { key: 'Housework', label: t.housework || 'Housework' },
      { key: 'Studying', label: t.studying || 'Studying' },
      { key: 'Unable to Work', label: t.unableToWork || 'Unable to Work' },
      { key: 'Sick/Disabled Persons', label: t.sickOrDisabled || 'Sick/Disabled Persons' },
      { key: 'Others', label: t.others || 'Others' }
    ];
    
    const laborForceData = laborForceCategories.map(category => {
      const value = regionData.Table1.Total[category.key];
      return value === 'n.a.' ? 0 : Math.round(parseFloat(value) || 0);
    });
    
    const nonLaborForceData = nonLaborForceCategories.map(category => {
      const value = regionData.Table1.Total[category.key];
      return value === 'n.a.' ? 0 : Math.round(parseFloat(value) || 0);
    });
    
    return {
      labels: [
        ...laborForceCategories.map(c => c.label),
        ...nonLaborForceCategories.map(c => c.label)
      ],
      datasets: [
        {
          label: t.inLaborForce || 'In Labor Force',
          data: [...laborForceData, ...Array(nonLaborForceCategories.length).fill(0)],
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
          stack: 'Stack 0',
        },
        {
          label: t.notInLaborForce || 'Not in Labor Force',
          data: [...Array(laborForceCategories.length).fill(0), ...nonLaborForceData],
          backgroundColor: 'rgba(255, 99, 132, 0.6)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
          stack: 'Stack 0',
        }
      ]
    };
  };

  // 修改农业与非农业数据准备函数
  const prepareAgriNonAgriData = () => {
    if (!regionData || !regionData.Table8 || !regionData.Table8.Total) return null;
    
    const agriSector = regionData.Table8.Total['Economic Activity - Agricultural Sector'];
    const nonAgriSector = regionData.Table8.Total['Economic Activity - Non-Agricultural Sector'];
    
    const validAgri = agriSector !== 'n.a.' ? Math.round(parseFloat(agriSector) || 0) : 0;
    const validNonAgri = nonAgriSector !== 'n.a.' ? Math.round(parseFloat(nonAgriSector) || 0) : 0;
    
    if (validAgri === 0 && validNonAgri === 0 && regionData.Table4 && regionData.Table4.Total) {
      const agriValue = regionData.Table4.Total['Agriculture, Forestry, and Fishing'];
      const agriEstimate = agriValue !== 'n.a.' ? Math.round(parseFloat(agriValue) || 0) : 0;
      
      const totalEmployed = regionData.Table1.Total['Employed Persons '];
      const totalEstimate = totalEmployed !== 'n.a.' ? Math.round(parseFloat(totalEmployed) || 0) : 0;
      const nonAgriEstimate = totalEstimate - agriEstimate;
      
      return {
        labels: [t.agricultural_sector || '农业部门', t.non_agricultural_sector || '非农业部门'],
        datasets: [
          {
            label: t.employment_by_sector || '按部门划分的就业情况',
            data: [agriEstimate, nonAgriEstimate],
            backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(153, 102, 255, 0.6)'],
            borderColor: ['rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'],
            borderWidth: 1,
          }
        ]
      };
    }
    
    return {
      labels: [t.agricultural_sector || '农业部门', t.non_agricultural_sector || '非农业部门'],
      datasets: [
        {
          label: t.employment_by_sector || '按部门划分的就业情况',
          data: [validAgri, validNonAgri],
          backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(153, 102, 255, 0.6)'],
          borderColor: ['rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'],
          borderWidth: 1,
        }
      ]
    };
  };

  const prepareEducationLevelData = () => {
    if (!regionData || !regionData.Table7 || !regionData.Table7.Total) return null;
    const educationLevels = [
      'No Education',
      'Primary Education',
      'lower Secondary Education',
      'Upper Secondary Education - Academic Track',
      'Upper Secondary Education - Vocational Track',
      'Higher Education - Professional Track',
      'Higher Education - Education Track',
    ];
    const data = educationLevels.map((level) => {
      const value = regionData.Table7.Total[level];
      return value === 'n.a.' ? 0 : Math.round(parseFloat(value) || 0);
    });
    return {
      labels: educationLevels.map((level) =>
        t[level.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_')] || level
      ),
      datasets: [
        {
          label: t.employedPersons || '就业人数',
          data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(201, 203, 207, 0.6)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(201, 203, 207, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  const prepareEmploymentStatusData = () => {
    if (!regionData || !regionData.Table5 || !regionData.Table5.Total) return null;
    const statuses = [
      'Employer',
      ' Government Employee',
      'Private Employee',
      ' Self-Employed',
      'Family Business Worker',
    ];
    const data = statuses.map((status) => {
      const value = regionData.Table5.Total[status];
      return value === 'n.a.' ? 0 : Math.round(parseFloat(value) || 0);
    });
    return {
      labels: statuses.map((status) => t[status.trim().toLowerCase().replace(/\s+/g, '_')] || status.trim()),
      datasets: [
        {
          label: t.employedPersons || '就业人数',
          data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // 修改图表标题和标签的显示方式，确保使用翻译对象
  const barOptions = {
    responsive: true,
    plugins: {
      legend: { position: 'top' },
      title: { display: true, text: t.employmentDistribution || '就业分布' },
    },
  };

  // 新增：堆叠柱状图配置
  const stackedBarOptions = {
    responsive: true,
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    plugins: {
      legend: { position: 'top' },
      title: { 
        display: true, 
        text: t.laborForceDetailedDistribution || 'Detailed Labor Force Distribution' 
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            if (value === 0) return null; // 不显示数值为0的提示
            return `${context.dataset.label}: ${value.toLocaleString()} ${t.persons || 'persons'}`;
          }
        }
      }
    },
  };

  // 修改饼图配置，确保使用翻译对象
  const pieOptions = {
    responsive: true,
    plugins: {
      legend: { position: 'top' },
      title: { display: true, text: t.laborForceParticipation || '劳动力参与情况' },
    },
  };

  const industryData = prepareEmploymentByIndustryData();
  const occupationData = prepareEmploymentByOccupationData();
  const laborForceData = prepareLaborForceParticipationData();
  const educationData = prepareEducationLevelData();
  const statusData = prepareEmploymentStatusData();
  // 替换为新的数据获取
  const employmentData = prepareEmploymentData();
  const otherLaborForceData = prepareOtherLaborForceData();
  const agriNonAgriData = prepareAgriNonAgriData();

  return (
    <div className="labor-data-charts">
      <h2>{t.laborMarketAnalysis || '劳动力市场分析'}</h2>
      <div className="region-selector">
        <label htmlFor="region-select">{t.selectRegion || '选择地区'}:</label>
        <select id="region-select" value={selectedRegion} onChange={handleRegionChange} className="region-select">
          {availableRegions.map((region) => (
            <option key={region} value={region}>
              {region === 'Regional Total'
                ? t.regional_total || '区域总计'
                : region === 'Prachin Buri'
                ? `${region} (${t.currentLocation || '当前位置'})`
                : `${region} (${t.nearbyArea || '周边地区'})`}
            </option>
          ))}
        </select>
      </div>
      <div className="region-info">
        <p>
          {selectedRegion === 'Regional Total'
            ? t.regionalTotalDescription || '显示泰国中部地区的总体劳动力数据'
            : selectedRegion === 'Prachin Buri'
            ? t.prachinBuriDescription || '304工业园所在地区的劳动力数据'
            : t.nearbyAreaDescription || '周边地区的劳动力数据，可作为参考'}
        </p>
      </div>
      <div className="labor-stats-summary">
        <div className="stat-card">
          <h3>{t.totalPopulation || '总人口 (15岁以上)'}</h3>
          <p className="stat-value">{Math.round(regionData.Table1.Total['Aged 15 and Over']).toLocaleString()}</p>
        </div>
        <div className="stat-card">
          <h3>{t.employedPersons || '就业人数'}</h3>
          <p className="stat-value">{Math.round(regionData.Table1.Total['Employed Persons ']).toLocaleString()}</p>
        </div>
        <div className="stat-card">
          <h3>{t.unemploymentRate || '失业率'}</h3>
          {/* 修复：不再乘以100，直接显示数据集中的失业率 */}
          <p className="stat-value">{regionData.Table1.Total['Unemployment Rate'].toFixed(2)}%</p>
        </div>
        <div className="stat-card">
          <h3>{t.laborForceParticipationRate || '劳动参与率'}</h3>
          <p className="stat-value">
            {(
              (regionData.Table1.Total[
                'Total （Total Labor Force， employed+unemployed+seasonally unemployed）'
              ] /
                regionData.Table1.Total['Aged 15 and Over']) *
              100
            ).toFixed(2)}
            %
          </p>
        </div>
        
        {/* 移除失业人数和制造业就业人数，保留其他两个统计卡片 */}
        <div className="stat-card">
          <h3>{t.manufacturingRatio || '制造业就业占比'}</h3>
          <p className="stat-value">
            {(
              (regionData.Table4.Total['Manufacturing'] / regionData.Table1.Total['Employed Persons ']) *
              100
            ).toFixed(2)}%
          </p>
        </div>
        <div className="stat-card">
          <h3>{t.higherEducationRatio || '高等教育比例'}</h3>
          <p className="stat-value">
            {(
              ((regionData.Table7.Total['Higher Education - Professional Track'] + 
                regionData.Table7.Total['Higher Education - Education Track']) / 
                regionData.Table1.Total['Employed Persons ']) *
              100
            ).toFixed(2)}%
          </p>
        </div>
      </div>
      <div className="charts-grid">
        <div className="chart-container">
          <h3>{t.employmentByIndustry || '按行业分类的就业情况'}</h3>
          {industryData ? <Bar data={industryData} options={{
            ...barOptions,
            plugins: {
              ...barOptions.plugins,
              title: { display: true, text: t.employmentByIndustry || '按行业分类的就业情况' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
        <div className="chart-container">
          <h3>{t.employmentByOccupation || '按职业分类的就业情况'}</h3>
          {occupationData ? <Bar data={occupationData} options={{
            ...barOptions,
            plugins: {
              ...barOptions.plugins,
              title: { display: true, text: t.employmentByOccupation || '按职业分类的就业情况' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>

        {/* 新增三个图表 */}
        <div className="chart-container">
          <h3>{t.employedPersons || '就业人数'}</h3>
          {employmentData ? <Bar data={employmentData} options={{
            ...barOptions,
            plugins: {
              ...barOptions.plugins,
              title: { display: true, text: t.employedPersons || '就业人数' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
        <div className="chart-container">
          <h3>{t.otherLaborCategories || '其他劳动力类别'}</h3>
          {otherLaborForceData ? <Bar data={otherLaborForceData} options={stackedBarOptions} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
        <div className="chart-container">
          <h3>{t.agriNonAgriRatio || '农业与非农业就业比例'}</h3>
          {agriNonAgriData ? <Pie data={agriNonAgriData} options={{
            ...pieOptions,
            plugins: {
              ...pieOptions.plugins,
              title: { display: true, text: t.agriNonAgriRatio || '农业与非农业就业比例' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>

        {/* 原教育水平图表 */}
        <div className="chart-container">
          <h3>{t.educationLevel || '就业人口教育水平'}</h3>
          {educationData ? <Bar data={educationData} options={{
            ...barOptions,
            plugins: {
              ...barOptions.plugins,
              title: { display: true, text: t.educationLevel || '就业人口教育水平' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>

        {/* 保留原有饼图 */}
        <div className="chart-container">
          <h3>{t.laborForceParticipation || '劳动力参与情况'}</h3>
          {laborForceData ? <Pie data={laborForceData} options={pieOptions} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
        <div className="chart-container">
          <h3>{t.employmentStatus || '就业状态分布'}</h3>
          {statusData ? <Pie data={statusData} options={{
            ...pieOptions,
            plugins: {
              ...pieOptions.plugins,
              title: { display: true, text: t.employmentStatus || '就业状态分布' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
      </div>
      <div className="labor-data-insights">
        <h3>{t.marketInsights || 'Market Insights'}</h3>
        <div className="insights-content">
          <p>
            {t.laborMarketInsight1 ||
              `The manufacturing sector employs ${regionData.Table4.Total[
                'Manufacturing'
              ].toLocaleString()} people in ${selectedRegion}, accounting for ${(
                (regionData.Table4.Total['Manufacturing'] / regionData.Table1.Total['Employed Persons ']) *
                100
              ).toFixed(2)}% of total employment, making it the main employment industry in this region.`}
          </p>
          <p>
            {t.laborMarketInsight2 ||
              `The region has a labor force participation rate of ${(
                (regionData.Table1.Total[
                  'Total （Total Labor Force， employed+unemployed+seasonally unemployed）'
                ] /
                  regionData.Table1.Total['Aged 15 and Over']) *
                100
              ).toFixed(2)}% and an unemployment rate of ${(regionData.Table1.Total['Unemployment Rate'] * 100).toFixed(2)}%.`}
          </p>
          <p>
            {t.laborMarketInsight3 ||
              `In terms of education, ${(
                ((regionData.Table7.Total['lower Secondary Education'] +
                  regionData.Table7.Total['Upper Secondary Education - Academic Track'] +
                  regionData.Table7.Total['Upper Secondary Education - Vocational Track'] +
                  regionData.Table7.Total['Higher Education - Professional Track'] +
                  regionData.Table7.Total['Higher Education - Education Track']) /
                  regionData.Table1.Total['Employed Persons ']) *
                100
              ).toFixed(2)}% of workers have secondary education or higher, indicating a relatively high-quality workforce in this region.`}
          </p>
        </div>
      </div>
      <div className="gender-comparison">
        <h3>{t.genderComparison || '性别对比分析'}</h3>
        <div className="gender-stats">
          <div className="gender-stat-card male">
            <h4>{t.male || '男性'}</h4>
            <p>
              {t.employedPersons || '就业人数'}:{' '}
              {Math.round(regionData.Table1.Male['Employed Persons ']).toLocaleString()}
            </p>
            <p>
              {t.unemploymentRate || '失业率'}:{' '}
              {(regionData.Table1.Male['Unemployment Rate'] * 100).toFixed(2)}%
            </p>
            <p>
              {t.laborForceParticipationRate || '劳动参与率'}:{' '}
              {(
                (regionData.Table1.Male[
                  'Total （Total Labor Force， employed+unemployed+seasonally unemployed）'
                ] /
                  regionData.Table1.Male['Aged 15 and Over']) *
                100
              ).toFixed(2)}
              %
            </p>
          </div>
          <div className="gender-stat-card female">
            <h4>{t.female || '女性'}</h4>
            <p>
              {t.employedPersons || '就业人数'}:{' '}
              {Math.round(regionData.Table1.Female['Employed Persons ']).toLocaleString()}
            </p>
            <p>
              {t.unemploymentRate || '失业率'}:{' '}
              {(regionData.Table1.Female['Unemployment Rate'] * 100).toFixed(2)}%
            </p>
            <p>
              {t.laborForceParticipationRate || '劳动参与率'}:{' '}
              {(
                (regionData.Table1.Female[
                  'Total （Total Labor Force， employed+unemployed+seasonally unemployed）'
                ] /
                  regionData.Table1.Female['Aged 15 and Over']) *
                100
              ).toFixed(2)}
              %
            </p>
          </div>
        </div>
      </div>
      <div className="data-source-info">
        <p className="data-source">
          {t.dataSource || '数据来源'}: {t.laborDataSource || '泰国国家统计局，2024年第四季度'}
        </p>
      </div>
    </div>
  );
};

export default LaborDataCharts;
