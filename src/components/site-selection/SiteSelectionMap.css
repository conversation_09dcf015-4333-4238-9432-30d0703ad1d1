/* 站点选择地图样式 */
.site-selection-map-container {
  position: relative;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* 专业地图控制器 */
.professional-map-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1000;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.map-style-switcher-pro {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  min-width: 200px;
}

.switcher-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 12px 16px;
}

.switcher-title {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.style-options {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.style-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  text-align: left;
  width: 100%;
}

.style-option:hover {
  background: rgba(77, 200, 255, 0.08);
  border-color: rgba(77, 200, 255, 0.2);
  color: #2c3e50;
  transform: translateY(-1px);
}

.style-option.active {
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.15) 0%, rgba(77, 200, 255, 0.08) 100%);
  border-color: rgba(77, 200, 255, 0.3);
  color: #1e40af;
  box-shadow: 0 2px 8px rgba(77, 200, 255, 0.2);
}

.style-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: inherit;
}

.style-name {
  font-weight: 500;
  color: inherit;
}

/* 站点标记样式 */
.site-selection-marker {
  animation: markerPulse 2s ease-in-out infinite;
  transition: all 0.3s ease;
}

.site-selection-marker.selected {
  animation: selectedMarkerGlow 1.5s ease-in-out infinite;
}

@keyframes markerPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(77, 200, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(77, 200, 255, 0.6);
  }
}

@keyframes selectedMarkerGlow {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 255, 136, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 255, 136, 0.8);
  }
}

/* 弹窗样式 */
.site-selection-popup .leaflet-popup-content-wrapper {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
}

.site-selection-popup .leaflet-popup-content {
  margin: 0;
  padding: 0;
  color: #ffffff;
}

.site-selection-popup .leaflet-popup-tip {
  background: #1a1a2e;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.site-marker-popup {
  padding: 16px;
  min-width: 220px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.popup-header h4 {
  color: #4dc8ff;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.parcel-id {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-family: monospace;
}

.popup-content {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
}

.info-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-item .value {
  color: #ffffff;
  font-weight: 600;
}

.select-popup-btn {
  width: 100%;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.15) 0%, rgba(77, 200, 255, 0.25) 100%);
  border: 1px solid rgba(77, 200, 255, 0.3);
  color: #4dc8ff;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-popup-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.25) 0%, rgba(77, 200, 255, 0.35) 100%);
  border-color: rgba(77, 200, 255, 0.5);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.2);
}

.select-popup-btn:disabled {
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.15) 0%, rgba(0, 255, 136, 0.25) 100%);
  border-color: rgba(0, 255, 136, 0.3);
  color: #00ff88;
  cursor: not-allowed;
}

/* ============== 新的横向弹窗样式 ============== */
.site-selection-popup-horizontal .leaflet-popup-content-wrapper {
  background: linear-gradient(135deg,
    rgba(26, 26, 46, 0.95) 0%,
    rgba(22, 33, 62, 0.95) 100%);
  border-radius: 16px;
  border: 1px solid rgba(77, 200, 255, 0.2);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(77, 200, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px) saturate(150%);
  overflow: hidden;
}

.site-selection-popup-horizontal .leaflet-popup-content {
  margin: 0;
  padding: 0;
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.site-selection-popup-horizontal .leaflet-popup-tip {
  background: rgba(26, 26, 46, 0.95);
  border: 1px solid rgba(77, 200, 255, 0.2);
}

.site-marker-popup-horizontal {
  padding: 0;
  min-width: 320px;
  max-width: 400px;
}

.horizontal-popup-container {
  display: flex;
  flex-direction: column;
  gap: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.02) 100%);
}

.popup-site-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px 12px 20px;
  background: linear-gradient(90deg,
    rgba(77, 200, 255, 0.08) 0%,
    rgba(77, 200, 255, 0.03) 100%);
  border-bottom: 1px solid rgba(77, 200, 255, 0.1);
}

.site-rank-badge {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #4dc8ff 0%, #00ff88 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
  color: #0a0f1c;
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.3);
  flex-shrink: 0;
}

.site-basic-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.site-id {
  font-size: 13px;
  font-weight: 600;
  color: #4dc8ff;
  font-family: 'JetBrains Mono', monospace;
  letter-spacing: 0.5px;
}

.site-cost {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.popup-details-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.02);
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  flex: 1;
}

.detail-value {
  font-size: 13px;
  font-weight: 600;
  color: #ffffff;
}

.detail-label {
  font-size: 10px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-separator {
  color: rgba(255, 255, 255, 0.2);
  font-size: 14px;
  margin: 0 16px;
}

.popup-actions {
  padding: 12px 20px 16px 20px;
  background: rgba(0, 0, 0, 0.1);
}

.select-popup-btn-horizontal {
  width: 100%;
  height: 36px;
  background: linear-gradient(135deg,
    rgba(77, 200, 255, 0.15) 0%,
    rgba(77, 200, 255, 0.25) 100%);
  border: 1px solid rgba(77, 200, 255, 0.3);
  color: #4dc8ff;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  text-transform: none;
  letter-spacing: 0.3px;
}

.select-popup-btn-horizontal:hover:not(.selected) {
  background: linear-gradient(135deg,
    rgba(77, 200, 255, 0.25) 0%,
    rgba(77, 200, 255, 0.35) 100%);
  border-color: rgba(77, 200, 255, 0.5);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(77, 200, 255, 0.2);
}

.select-popup-btn-horizontal.selected {
  background: linear-gradient(135deg,
    rgba(0, 255, 136, 0.2) 0%,
    rgba(0, 255, 136, 0.3) 100%);
  border-color: rgba(0, 255, 136, 0.4);
  color: #00ff88;
  cursor: default;
  box-shadow: 0 4px 16px rgba(0, 255, 136, 0.2);
}

.select-popup-btn-horizontal.selected:hover {
  transform: none;
  box-shadow: 0 4px 16px rgba(0, 255, 136, 0.2);
}

/* 空状态样式 */
.map-empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  z-index: 1000;
  background: rgba(10, 15, 28, 0.8);
  padding: 24px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.map-empty-state p {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .map-style-switcher {
    top: 8px;
    right: 8px;
    padding: 3px;
  }

  .style-btn {
    padding: 6px;
    font-size: 14px;
    min-width: 28px;
    height: 28px;
  }

  .site-marker-popup {
    padding: 12px;
    min-width: 200px;
  }

  .popup-header h4 {
    font-size: 13px;
  }

  .parcel-id {
    font-size: 11px;
  }

  .info-item {
    font-size: 11px;
  }

  .select-popup-btn {
    padding: 6px 10px;
    font-size: 11px;
  }

  /* 横向弹窗响应式 */
  .site-marker-popup-horizontal {
    min-width: 280px;
    max-width: 320px;
  }

  .popup-site-info {
    padding: 12px 16px 10px 16px;
  }

  .site-rank-badge {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .site-id {
    font-size: 12px;
  }

  .site-cost {
    font-size: 14px;
  }

  .popup-details-row {
    padding: 10px 16px;
  }

  .detail-value {
    font-size: 12px;
  }

  .detail-label {
    font-size: 9px;
  }

  .popup-actions {
    padding: 10px 16px 12px 16px;
  }

  .select-popup-btn-horizontal {
    height: 32px;
    font-size: 12px;
  }

  .map-empty-state {
    padding: 20px;
  }

  .empty-icon {
    font-size: 36px;
  }

  .map-empty-state p {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .site-marker-popup {
    padding: 10px;
    min-width: 180px;
  }

  .popup-header {
    margin-bottom: 10px;
    padding-bottom: 6px;
  }

  .popup-header h4 {
    font-size: 12px;
  }

  .parcel-id {
    font-size: 10px;
  }

  .info-item {
    font-size: 10px;
    margin-bottom: 4px;
  }

  .select-popup-btn {
    padding: 6px 8px;
    font-size: 10px;
  }

  /* 小屏幕横向弹窗 */
  .site-marker-popup-horizontal {
    min-width: 260px;
    max-width: 300px;
  }

  .popup-site-info {
    padding: 10px 14px 8px 14px;
    gap: 10px;
  }

  .site-rank-badge {
    width: 26px;
    height: 26px;
    font-size: 11px;
  }

  .site-id {
    font-size: 11px;
  }

  .site-cost {
    font-size: 13px;
  }

  .popup-details-row {
    padding: 8px 14px;
  }

  .detail-value {
    font-size: 11px;
  }

  .detail-label {
    font-size: 8px;
  }

  .detail-separator {
    margin: 0 12px;
    font-size: 12px;
  }

  .popup-actions {
    padding: 8px 14px 10px 14px;
  }

  .select-popup-btn-horizontal {
    height: 30px;
    font-size: 11px;
  }

  .map-empty-state {
    padding: 16px;
  }

  .empty-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .map-empty-state p {
    font-size: 12px;
  }
}
