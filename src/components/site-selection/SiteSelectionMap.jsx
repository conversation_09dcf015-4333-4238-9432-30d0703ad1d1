/**
 * 🏭 Site Selection Map Component
 * 站点选择地图组件 - 显示勘探结果的地图视图
 */

import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>ontainer, Tile<PERSON><PERSON>er, <PERSON><PERSON>, Popup, useMap } from 'react-leaflet';
import L from 'leaflet';
import { formatFinancialValue } from '../../services/siteSelectionService';
import './SiteSelectionMap.css';

// 地图样式配置
const mapTileUrls = {
  street: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
  satellite: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
  terrain: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png'
};

const mapAttributions = {
  street: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
  satellite: '&copy; <a href="https://www.esri.com/">Esri</a>',
  terrain: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://opentopomap.org">OpenTopoMap</a>'
};

// 创建站点选择标记图标
const createSiteMarker = (rank, isSelected = false) => {
  const color = isSelected ? '#00ff88' : '#4dc8ff';
  const size = isSelected ? 40 : 32;
  
  const iconHtml = `
    <div class="site-selection-marker ${isSelected ? 'selected' : ''}" style="
      width: ${size}px;
      height: ${size}px;
      background: linear-gradient(135deg, ${color} 0%, ${color}aa 100%);
      border: 2px solid ${color};
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #0a0f1c;
      font-weight: 700;
      font-size: ${size > 35 ? '14px' : '12px'};
      box-shadow: 0 4px 12px rgba(77, 200, 255, 0.4);
      position: relative;
      z-index: ${isSelected ? 1000 : 100};
    ">
      ${rank}
    </div>
  `;
  
  return L.divIcon({
    className: '',
    html: iconHtml,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
    popupAnchor: [0, -size / 2 - 5]
  });
};

// 地图控制器组件
const MapController = ({ parcels, selectedParcel, onParcelSelect, onResetView }) => {
  const map = useMap();
  const markersRef = useRef([]);
  const hasInitializedView = useRef(false);

  // 添加地图拖拽事件处理，用于触发玻璃折射效果
  useEffect(() => {
    const mapContainer = map.getContainer().closest('.liquid-map-container');

    const handleDragStart = () => {
      if (mapContainer) {
        mapContainer.classList.add('dragging');
      }
    };

    const handleDragEnd = () => {
      if (mapContainer) {
        // 延迟移除类名，让折射动画完成
        setTimeout(() => {
          mapContainer.classList.remove('dragging');
        }, 1500);
      }
    };

    map.on('dragstart', handleDragStart);
    map.on('dragend', handleDragEnd);

    return () => {
      map.off('dragstart', handleDragStart);
      map.off('dragend', handleDragEnd);
    };
  }, [map]);

  useEffect(() => {
    if (!parcels || parcels.length === 0) return;

    // 清除现有标记
    markersRef.current.forEach(marker => {
      map.removeLayer(marker);
    });
    markersRef.current = [];

    // 添加新标记
    parcels.forEach((parcel, index) => {
      const rank = index + 1;
      const isSelected = selectedParcel?.parcel_id === parcel.parcel_id;
      // 修复: 使用正确的location属性
      const position = [parcel.location?.lat || 0, parcel.location?.lng || 0]; // [lat, lng]

      const marker = L.marker(position, {
        icon: createSiteMarker(rank, isSelected),
        zIndexOffset: isSelected ? 1000 : 100
      });

      // 创建扁平横向弹窗内容
      const popupContent = document.createElement('div');
      popupContent.className = 'site-marker-popup-horizontal';
      popupContent.innerHTML = `
        <div class="horizontal-popup-container">
          <div class="popup-site-info">
            <div class="site-rank-badge">${rank}</div>
            <div class="site-basic-info">
              <span class="site-id">${parcel.parcel_id}</span>
              <span class="site-cost">${formatFinancialValue(parcel.total_cost)}</span>
            </div>
          </div>
          <div class="popup-details-row">
            <div class="detail-item">
              <span class="detail-value">${parcel.area_sqft?.toLocaleString()}</span>
              <span class="detail-label">sq ft</span>
            </div>
            <div class="detail-separator">|</div>
            <div class="detail-item">
              <span class="detail-value">${(parcel.location?.lat || 0).toFixed(3)}, ${(parcel.location?.lng || 0).toFixed(3)}</span>
              <span class="detail-label">坐标</span>
            </div>
          </div>
          <div class="popup-actions">
            <button class="select-popup-btn-horizontal ${isSelected ? 'selected' : ''}" data-parcel-id="${parcel.parcel_id}">
              ${isSelected ? '✓ 已选择' : '选择'}
            </button>
          </div>
        </div>
      `;

      // 添加按钮点击事件
      const selectBtn = popupContent.querySelector('.select-popup-btn-horizontal');
      selectBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isSelected) {
          onParcelSelect(parcel);
          // 自动缩放到选中的地块位置，使用相同的偏移逻辑
          const currentZoom = map.getZoom();
          const targetZoom = Math.max(currentZoom, 15);

          if (currentZoom < 15) {
            // 计算偏移位置，让marker显示在地图下方1/3处
            const mapSize = map.getSize();
            const offsetY = mapSize.y * 0.15;
            const targetPoint = map.project(position, targetZoom);
            const offsetPoint = L.point(targetPoint.x, targetPoint.y - offsetY);
            const offsetLatLng = map.unproject(offsetPoint, targetZoom);

            map.flyTo(offsetLatLng, targetZoom, {
              duration: 1.0,
              easeLinearity: 0.25
            });
          }
        }
        map.closePopup();
      });

      marker.bindPopup(popupContent, {
        minWidth: 320,
        maxWidth: 400,
        className: 'site-selection-popup-horizontal',
        offset: [0, -10] // 调整弹窗位置，避免遮挡顶部按钮
      });

      // 修复marker点击事件 - 避免回弹问题，调整缩放位置让marker靠下
      marker.on('click', () => {
        // 添加缩放指示器
        const mapContainer = map.getContainer().closest('.liquid-map-container');
        if (mapContainer) {
          mapContainer.classList.add('zooming');
          setTimeout(() => {
            mapContainer.classList.remove('zooming');
          }, 1000);
        }

        // 智能缩放：只有当前缩放级别小于15时才缩放，避免回弹
        const currentZoom = map.getZoom();
        const targetZoom = Math.max(currentZoom, 15);

        if (currentZoom < 15) {
          // 计算偏移位置，让marker显示在地图下方1/3处，避免与顶部按钮重叠
          const mapSize = map.getSize();
          const offsetY = mapSize.y * 0.15; // 向下偏移15%的地图高度
          const targetPoint = map.project(position, targetZoom);
          const offsetPoint = L.point(targetPoint.x, targetPoint.y - offsetY);
          const offsetLatLng = map.unproject(offsetPoint, targetZoom);

          map.flyTo(offsetLatLng, targetZoom, {
            duration: 1.0,
            easeLinearity: 0.25
          });
        }

        // 如果不是当前选中的地块，则选中它
        if (!isSelected) {
          setTimeout(() => {
            onParcelSelect(parcel);
          }, currentZoom < 15 ? 500 : 100); // 根据是否需要缩放调整延迟
        }
      });

      marker.addTo(map);
      markersRef.current.push(marker);
    });

    // 修复: 在初始加载时调整地图视图，不管是否有选中的地块
    if (parcels.length > 0 && !hasInitializedView.current) {
      const bounds = L.latLngBounds(
        parcels.map(parcel => [parcel.location?.lat || 0, parcel.location?.lng || 0])
      );
      map.fitBounds(bounds, { padding: [20, 20] });
      hasInitializedView.current = true;
    }

    // 暴露重置视图方法给父组件
    if (onResetView) {
      onResetView(() => {
        if (parcels.length > 0) {
          const bounds = L.latLngBounds(
            parcels.map(parcel => [parcel.location?.lat || 0, parcel.location?.lng || 0])
          );
          map.flyToBounds(bounds, {
            padding: [20, 20],
            duration: 1.2,
            easeLinearity: 0.25
          });
        }
      });
    }

    return () => {
      markersRef.current.forEach(marker => {
        map.removeLayer(marker);
      });
      markersRef.current = [];
    };
  }, [map, parcels, selectedParcel, onParcelSelect, onResetView]);

  return null;
};



// 主组件
const SiteSelectionMap = ({
  parcels = [],
  selectedParcel = null,
  onParcelSelect = () => {},
  mapStyle: externalMapStyle = 'satellite',
  height = '500px',
  className = ''
}) => {
  // 使用外部传入的mapStyle，如果没有则使用默认值
  const mapStyle = externalMapStyle || 'satellite';
  const mapRef = useRef(null);
  const resetViewRef = useRef(null);

  // 计算地图中心点
  const getMapCenter = () => {
    if (!parcels || parcels.length === 0) {
      return [39.8283, -98.5795]; // 美国中心
    }
    
    // 修复: 使用正确的location属性，并添加安全检查
    const avgLat = parcels.reduce((sum, parcel) => sum + (parcel.location?.lat || 0), 0) / parcels.length;
    const avgLng = parcels.reduce((sum, parcel) => sum + (parcel.location?.lng || 0), 0) / parcels.length;
    
    return [avgLat, avgLng];
  };

  const mapCenter = getMapCenter();

  return (
    <div className={`site-selection-map-container ${className}`} style={{ height }}>
      <MapContainer
        center={mapCenter}
        zoom={8}
        style={{ height: '100%', width: '100%', borderRadius: '12px' }}
        ref={mapRef}
        zoomControl={false}
      >
        <TileLayer
          url={mapTileUrls[mapStyle]}
          attribution={mapAttributions[mapStyle]}
        />
        
        <MapController
          parcels={parcels}
          selectedParcel={selectedParcel}
          onParcelSelect={onParcelSelect}
          onResetView={(resetFn) => { resetViewRef.current = resetFn; }}
        />
      </MapContainer>

      {/* 液态玻璃一键返回宏观视图按钮 */}
      {parcels.length > 0 && (
        <button
          className="liquid-reset-view-button"
          onClick={() => resetViewRef.current && resetViewRef.current()}
          title="返回宏观视图 - Reset to Overview"
        >
          <div className="reset-btn-glass-layer"></div>
          <div className="reset-btn-content">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="reset-btn-icon">
              <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" stroke="currentColor" strokeWidth="2"/>
              <path d="M3 3v5h5" stroke="currentColor" strokeWidth="2"/>
              <circle cx="12" cy="12" r="1" fill="currentColor"/>
            </svg>
            <span className="reset-btn-label">宏观</span>
          </div>
          <div className="reset-btn-refraction"></div>
        </button>
      )}

      {parcels.length === 0 && (
        <div className="map-empty-state">
          <div className="empty-icon">🗺️</div>
          <p>暂无地块数据</p>
        </div>
      )}
    </div>
  );
};

export default SiteSelectionMap;
