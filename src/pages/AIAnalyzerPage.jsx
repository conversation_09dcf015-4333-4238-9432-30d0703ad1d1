import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { authAPI, projectAPI } from '../services/api';
import '../styles/AIAnalyzerPage.css';

const AIAnalyzerPage = () => {
  const navigate = useNavigate();
  const dropdownRef = useRef(null);
  const [projects, setProjects] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [language, setLanguage] = useState(() => {
    // 优先使用用户登录后的语言偏好
    const userLang = localStorage.getItem('preferredLanguage');
    return userLang || 'en';
  });
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'my', 'favorites'
  const [newProject, setNewProject] = useState({
    name: '',
    description: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [authError, setAuthError] = useState(null);

  // 翻译对象
  const translations = {
    en: {
      title: 'AI Site Analyzer',
      subtitle: 'Intelligent Industrial Site Analysis Platform',
      createProject: 'Create New Project',
      projectName: 'Project Name',
      projectDescription: 'Project Description',
      create: 'Create',
      cancel: 'Cancel',
      noProjects: 'No projects yet',
      noProjectsDesc: 'Create your first project to start analyzing industrial sites',
      createdAt: 'Created',
      openProject: 'Open Project',
      deleteProject: 'Delete',
      backToHome: 'Back to Home',
      selectLanguage: 'Select Language',
      myProjects: 'My Projects',
      allProjects: 'All Projects',
      favorites: 'Favorites',
      starProject: 'Star',
      unstarProject: 'Unstar',
      noFavorites: 'No favorite projects yet',
      noFavoritesDesc: 'Star your important projects to access them quickly',
      loadingProjects: 'Loading projects...',
      projectLoadError: 'Failed to load projects. Please try again.',
      connectionError: 'Connection error. Please check your network.',
      authError: 'Authentication failed. Please login again.',
      retryButton: 'Retry',
      dismissError: 'Dismiss',
      loginRequired: 'Login Required',
      loginToCreateProjects: 'Please login to create and manage projects',
      loginButton: 'Login',
      continueAsGuest: 'Continue as Guest',
      sessionExpired: 'Your session has expired. Please login again.',
      authenticationStatus: 'Authentication Status',
      loggedInAs: 'Logged in as',
      welcome: 'Welcome'
    },
    zh: {
      title: 'AI选址分析器',
      subtitle: '智能工业选址分析平台',
      createProject: '创建新项目',
      projectName: '项目名称',
      projectDescription: '项目描述',
      create: '创建',
      cancel: '取消',
      noProjects: '暂无项目',
      noProjectsDesc: '创建您的第一个项目开始分析工业选址',
      createdAt: '创建时间',
      openProject: '打开项目',
      deleteProject: '删除',
      backToHome: '返回首页',
      selectLanguage: '选择语言',
      myProjects: '我的项目',
      allProjects: '所有项目',
      favorites: '收藏项目',
      starProject: '收藏',
      unstarProject: '取消收藏',
      noFavorites: '暂无收藏项目',
      noFavoritesDesc: '收藏重要项目以便快速访问',
      loadingProjects: '正在加载项目...',
      projectLoadError: '加载项目失败，请重试',
      connectionError: '连接错误，请检查网络连接',
      authError: '认证失败，请重新登录',
      retryButton: '重试',
      dismissError: '关闭',
      loginRequired: '需要登录',
      loginToCreateProjects: '请登录以创建和管理项目',
      loginButton: '登录',
      continueAsGuest: '以访客身份继续',
      sessionExpired: '您的会话已过期，请重新登录',
      authenticationStatus: '认证状态',
      loggedInAs: '已登录为',
      welcome: '欢迎'
    }
  };

  const t = translations[language] || translations.en;





  // 检查认证状态的函数
  const checkAuthenticationStatus = useCallback(() => {
    const isAuth = authAPI.isAuthenticated();
    const user = authAPI.getUser();

    setIsAuthenticated(isAuth);
    setCurrentUser(user);

    // 如果用户有语言偏好，应用它
    if (user && user.preferred_language) {
      setLanguage(user.preferred_language);
      localStorage.setItem('preferredLanguage', user.preferred_language);
    }

    return { isAuth, user };
  }, []);

  // 只在组件挂载时执行初始化
  useEffect(() => {
    const initializeComponent = async () => {
      // 检查认证状态
      const { isAuth } = checkAuthenticationStatus();

      if (!isAuth) {
        console.log('AIAnalyzerPage: 用户未认证，重定向到登录页面');
        navigate('/login');
        return;
      }

      // 加载项目
      setIsLoading(true);
      setError(null);

      try {
        console.log('正在加载项目列表...');
        const response = await projectAPI.getProjects();
        console.log('项目API响应:', response);

        if (response.success) {
          // 转换数据库字段到前端格式
          const formattedProjects = response.data.projects.map(project => ({
            id: project.id.toString(),
            name: project.project_name,
            description: project.description || '',
            createdAt: project.created_at,
            updatedAt: project.updated_at,
            status: project.status,
            starred: Boolean(project.isFavorite) // 使用数据库中的收藏状态
          }));



          setProjects(formattedProjects);
          console.log('项目加载成功:', formattedProjects.length, '个项目');
          console.log('项目详细信息:', formattedProjects);
        } else {
          throw new Error(response.message || 'API返回失败状态');
        }
      } catch (error) {
        console.error('加载项目失败:', error);

        // 根据错误类型设置不同的错误消息
        if (error.message.includes('401') || error.message.includes('403')) {
          setError(language === 'zh' ? '认证失败，请重新登录' : 'Authentication failed. Please login again.');
          setTimeout(() => navigate('/login'), 2000);
        } else if (error.message.includes('fetch') || error.message.includes('network')) {
          setError(language === 'zh' ? '连接错误，请检查网络连接' : 'Connection error. Please check your network.');
        } else {
          setError(language === 'zh' ? '加载项目失败，请重试' : 'Failed to load projects. Please try again.');
        }
      } finally {
        setIsLoading(false);
      }
    };

    initializeComponent();
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 处理点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowLanguageDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 处理未认证用户尝试创建项目
  const handleUnauthenticatedCreateProject = useCallback(() => {
    setShowLoginPrompt(true);
  }, []);

  // 处理登录提示的登录按钮点击
  const handleLoginFromPrompt = useCallback(() => {
    setShowLoginPrompt(false);
    navigate('/login');
  }, [navigate]);

  // 创建新项目
  const handleCreateProject = useCallback(async () => {
    // 首先检查认证状态
    if (!authAPI.isAuthenticated()) {
      setAuthError(t.sessionExpired);
      setShowLoginPrompt(true);
      return;
    }

    if (!newProject.name.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await projectAPI.createProject({
        project_name: newProject.name.trim(),
        description: newProject.description.trim()
      });

      if (response.success) {
        // 转换数据库字段到前端格式
        const formattedProject = {
          id: response.data.project.id.toString(),
          name: response.data.project.project_name,
          description: response.data.project.description || '',
          createdAt: response.data.project.created_at,
          updatedAt: response.data.project.updated_at,
          status: response.data.project.status,
          starred: Boolean(response.data.project.isFavorite) // 使用数据库中的收藏状态
        };

        setProjects(prev => [formattedProject, ...prev]);
        setNewProject({ name: '', description: '' });
        setShowCreateModal(false);
      }
    } catch (error) {
      console.error('创建项目失败:', error);
      setError('创建项目失败，请重试');
    } finally {
      setIsLoading(false);
    }
  }, [newProject, t]);

  // 删除项目
  const handleDeleteProject = useCallback(async (projectId) => {
    console.log('=== 删除项目开始 ===');
    console.log('项目ID:', projectId);
    console.log('用户认证状态:', authAPI.isAuthenticated());

    // 检查认证状态
    if (!authAPI.isAuthenticated()) {
      setAuthError(language === 'zh' ? '会话已过期，请重新登录' : 'Session expired. Please login again.');
      setShowLoginPrompt(true);
      return;
    }

    const confirmMessage = language === 'zh'
      ? '确定要删除这个项目吗？此操作无法撤销。'
      : 'Are you sure you want to delete this project? This action cannot be undone.';

    if (!confirm(confirmMessage)) {
      console.log('用户取消删除操作');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('正在调用删除API...');
      const response = await projectAPI.deleteProject(projectId);
      console.log('删除API响应:', response);

      if (response.success) {
        // 从本地状态中移除项目
        setProjects(prev => prev.filter(p => p.id !== projectId));
        console.log('项目删除成功，已从本地状态移除');

        // 显示成功消息（可选）
        const successMessage = language === 'zh' ? '项目删除成功' : 'Project deleted successfully';
        console.log(successMessage);
      } else {
        throw new Error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除项目失败:', error);

      // 根据错误类型设置不同的错误消息
      if (error.message.includes('401') || error.message.includes('403')) {
        setAuthError(language === 'zh' ? '认证失败，请重新登录' : 'Authentication failed. Please login again.');
        setShowLoginPrompt(true);
      } else if (error.message.includes('404')) {
        setError(language === 'zh' ? '项目不存在或已被删除' : 'Project not found or already deleted');
        // 从本地状态中移除项目（可能已在服务器端删除）
        setProjects(prev => prev.filter(p => p.id !== projectId));
      } else {
        setError(language === 'zh' ? '删除项目失败，请重试' : 'Failed to delete project. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  }, [language]);

  // 打开项目
  const handleOpenProject = useCallback((projectId) => {
    console.log('=== 开始打开项目 ===');
    console.log('项目ID:', projectId);
    console.log('项目ID类型:', typeof projectId);
    console.log('navigate函数:', navigate);

    if (!projectId) {
      console.error('项目ID为空，无法导航');
      alert('项目ID为空，无法导航');
      return;
    }

    if (!navigate) {
      console.error('navigate函数不可用');
      alert('导航函数不可用');
      return;
    }

    const targetUrl = `/ai-project/${projectId}`;
    console.log('目标URL:', targetUrl);

    try {
      console.log('执行导航...');
      navigate(targetUrl);
      console.log('导航命令已执行');
    } catch (error) {
      console.error('导航失败:', error);
      alert(`导航失败: ${error.message}`);
    }
  }, [navigate]);

  // 切换项目收藏状态 (同步到数据库)
  const toggleProjectStar = useCallback(async (projectId) => {
    // 检查认证状态
    if (!authAPI.isAuthenticated()) {
      setAuthError(language === 'zh' ? '会话已过期，请重新登录' : 'Session expired. Please login again.');
      setShowLoginPrompt(true);
      return;
    }

    try {
      // 先更新本地状态以提供即时反馈
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? { ...project, starred: !project.starred }
          : project
      ));

      // 调用API同步到数据库
      const response = await projectAPI.toggleFavorite(projectId);

      if (response.success) {
        // API成功，使用服务器返回的状态更新本地状态
        setProjects(prev => prev.map(project =>
          project.id === projectId
            ? { ...project, starred: response.data.isFavorite }
            : project
        ));
        console.log('收藏状态更新成功:', response.data.isFavorite ? '已收藏' : '已取消收藏');
      } else {
        // API失败，回滚本地状态
        setProjects(prev => prev.map(project =>
          project.id === projectId
            ? { ...project, starred: !project.starred }
            : project
        ));
        throw new Error(response.message || '更新收藏状态失败');
      }
    } catch (error) {
      console.error('切换收藏状态失败:', error);

      // 回滚本地状态
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? { ...project, starred: !project.starred }
          : project
      ));

      // 根据错误类型设置不同的错误消息
      if (error.message.includes('401') || error.message.includes('403')) {
        setAuthError(language === 'zh' ? '认证失败，请重新登录' : 'Authentication failed. Please login again.');
        setShowLoginPrompt(true);
      } else {
        setError(language === 'zh' ? '更新收藏状态失败，请重试' : 'Failed to update favorite status. Please try again.');
      }
    }
  }, [language]);

  // 语言切换
  const handleLanguageChange = useCallback((newLang) => {
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
    setShowLanguageDropdown(false);
  }, []);

  // 重试加载项目
  const handleRetryLoadProjects = useCallback(() => {
    if (!authAPI.isAuthenticated()) {
      setError(language === 'zh' ? '认证失败，请重新登录' : 'Authentication failed. Please login again.');
      return;
    }

    setIsLoading(true);
    setError(null);

    projectAPI.getProjects()
      .then(response => {
        if (response.success) {
          const formattedProjects = response.data.projects.map(project => ({
            id: project.id.toString(),
            name: project.project_name,
            description: project.description || '',
            createdAt: project.created_at,
            updatedAt: project.updated_at,
            status: project.status,
            starred: Boolean(project.isFavorite) // 使用数据库中的收藏状态
          }));
          setProjects(formattedProjects);
        } else {
          throw new Error(response.message || 'API返回失败状态');
        }
      })
      .catch(error => {
        console.error('重试加载项目失败:', error);
        if (error.message.includes('401') || error.message.includes('403')) {
          setError(language === 'zh' ? '认证失败，请重新登录' : 'Authentication failed. Please login again.');
          setTimeout(() => navigate('/login'), 2000);
        } else if (error.message.includes('fetch') || error.message.includes('network')) {
          setError(language === 'zh' ? '连接错误，请检查网络连接' : 'Connection error. Please check your network.');
        } else {
          setError(language === 'zh' ? '加载项目失败，请重试' : 'Failed to load projects. Please try again.');
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [language, navigate]);

  // 根据当前选项卡筛选项目 - 使用 useMemo 优化性能
  const displayedProjects = useMemo(() => {
    switch(activeTab) {
      case 'favorites':
        return projects.filter(p => p.starred);
      case 'my':
        // 这里可以根据用户ID等筛选"我的项目"
        // 目前只是演示，所以返回所有项目
        return projects;
      case 'all':
      default:
        return projects;
    }
  }, [projects, activeTab]);

  return (
    <div className="ai-analyzer-page">
      {/* 背景效果 - 增强版 */}
      <div className="ai-background">
        <div className="grid-pattern"></div>
        <div className="floating-particles"></div>
        <div className="circuit-pattern"></div>
        <div className="glow-effect"></div>
      </div>

      {/* 悬浮导航按钮 */}
      <div className="floating-nav-buttons">
        {/* 返回首页按钮 - 左上角 */}
        <button
          className="floating-btn back-floating-btn"
          onClick={() => navigate('/')}
          title={t.backToHome}
        >
          <span className="btn-icon">←</span>
          <span className="btn-text">{t.backToHome}</span>
        </button>

        {/* 页面标题 - 居中悬浮 */}
        <div className="floating-title">
          <h1 className="page-title">{t.title}</h1>
        </div>

        {/* 右侧按钮组 */}
        <div className="floating-right-group">
          {/* 用户欢迎信息 - 始终渲染以保持布局稳定 */}
          <button
            className="floating-btn welcome-floating-btn"
            style={{
              visibility: isAuthenticated && currentUser ? 'visible' : 'hidden',
              opacity: isAuthenticated && currentUser ? 1 : 0
            }}
            disabled={!isAuthenticated || !currentUser}
          >
            <span className="welcome-icon">👋</span>
            <span className="welcome-text">
              {isAuthenticated && currentUser
                ? `${t.welcome} ${currentUser.username || currentUser.email}`
                : t.welcome
              }
            </span>
          </button>

          {/* 语言切换按钮 */}
          <div className="floating-language-switcher">
            <div className="language-dropdown" ref={dropdownRef}>
              <button
                className="floating-btn language-floating-btn"
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
                title={t.selectLanguage}
              >
                <span className="language-flag">
                  {language === 'en' ? '🇺🇸' : '🇨🇳'}
                </span>
                <span className="language-text">
                  {language === 'en' ? 'EN' : '中文'}
                </span>
                <span className={`dropdown-arrow ${showLanguageDropdown ? 'open' : ''}`}>
                  ▼
                </span>
              </button>

              {showLanguageDropdown && (
                <div className="floating-language-menu">
                  <button
                    className={`language-option ${language === 'en' ? 'active' : ''}`}
                    onClick={() => handleLanguageChange('en')}
                  >
                    <span className="option-flag">🇺🇸</span>
                    <span className="option-text">English</span>
                  </button>
                  <button
                    className={`language-option ${language === 'zh' ? 'active' : ''}`}
                    onClick={() => handleLanguageChange('zh')}
                  >
                    <span className="option-flag">🇨🇳</span>
                    <span className="option-text">中文</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 主内容 - 全新简洁设计 */}
      <main className="main-content">
        <div className="content-container">

          {/* 操作栏 */}
          <div className="action-bar">
            <button
              className={`create-btn ${!isAuthenticated ? 'disabled' : ''}`}
              onClick={isAuthenticated ? () => setShowCreateModal(true) : handleUnauthenticatedCreateProject}
              disabled={!isAuthenticated}
            >
              + {t.createProject}
            </button>

            <div className="filter-tabs">
              <button
                className={`tab-btn ${activeTab === 'all' ? 'active' : ''}`}
                onClick={() => setActiveTab('all')}
              >
                {t.allProjects}
              </button>
              <button
                className={`tab-btn ${activeTab === 'favorites' ? 'active' : ''}`}
                onClick={() => setActiveTab('favorites')}
              >
                {t.favorites}
              </button>
            </div>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="error-message">
              <span>⚠️ {error}</span>
              <button onClick={handleRetryLoadProjects} className="retry-btn">
                重试
              </button>
            </div>
          )}

          {/* 项目内容区 */}
          <div className="projects-section">
            {isLoading ? (
              <div className="loading-state">
                <div className="spinner"></div>
                <p>{t.loadingProjects}</p>
              </div>
            ) : displayedProjects.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">
                  {activeTab === 'favorites' ? '⭐' : '📊'}
                </div>
                <h3>{activeTab === 'favorites' ? t.noFavorites : t.noProjects}</h3>
                <p>{activeTab === 'favorites' ? t.noFavoritesDesc : t.noProjectsDesc}</p>
                {activeTab !== 'favorites' && (
                  <button
                    className={`create-btn ${!isAuthenticated ? 'disabled' : ''}`}
                    onClick={isAuthenticated ? () => setShowCreateModal(true) : handleUnauthenticatedCreateProject}
                    disabled={!isAuthenticated}
                  >
                    {t.createProject}
                  </button>
                )}
              </div>
            ) : (
              <div className="projects-list">
                {displayedProjects.map(project => (
                  <div key={project.id} className="project-item">
                    <div className="project-info" onClick={() => handleOpenProject(project.id)}>
                      <div className="project-header">
                        <h4 className="project-title">{project.name}</h4>
                        <button
                          className={`star-button ${project.starred ? 'starred' : ''}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleProjectStar(project.id);
                          }}
                        >
                          {project.starred ? '★' : '☆'}
                        </button>
                      </div>
                      {project.description && (
                        <p className="project-desc">{project.description}</p>
                      )}
                      <div className="project-date">
                        {t.createdAt}: {new Date(project.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="project-actions">
                      <button
                        className="action-button open"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleOpenProject(project.id);
                        }}
                      >
                        打开
                      </button>
                      <button
                        className="action-button delete"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteProject(project.id);
                        }}
                      >
                        删除
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>

      {/* 登录提示模态框 */}
      {showLoginPrompt && (
        <div className="modal-overlay">
          <div className="modal-content login-prompt-modal">
            <div className="login-prompt-header">
              <h2>{t.loginRequired}</h2>
              <button
                className="modal-close-btn"
                onClick={() => setShowLoginPrompt(false)}
              >
                ×
              </button>
            </div>
            <div className="login-prompt-body">
              <div className="login-prompt-icon">
                🔐
              </div>
              <p className="login-prompt-message">
                {authError || t.loginToCreateProjects}
              </p>
            </div>
            <div className="login-prompt-actions">
              <button
                className="btn-secondary"
                onClick={() => setShowLoginPrompt(false)}
              >
                {t.continueAsGuest}
              </button>
              <button
                className="btn-primary"
                onClick={handleLoginFromPrompt}
              >
                {t.loginButton}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 创建项目模态框 */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>{t.createProject}</h2>
            <div className="form-group">
              <label>{t.projectName}</label>
              <input
                type="text"
                value={newProject.name}
                onChange={(e) => setNewProject(prev => ({ ...prev, name: e.target.value }))}
                placeholder={t.projectName}
                maxLength={100}
              />
            </div>
            <div className="form-group">
              <label>{t.projectDescription}</label>
              <textarea
                value={newProject.description}
                onChange={(e) => setNewProject(prev => ({ ...prev, description: e.target.value }))}
                placeholder={t.projectDescription}
                rows={3}
                maxLength={500}
              />
            </div>
            <div className="modal-actions">
              <button
                className="btn-secondary"
                onClick={() => {
                  setShowCreateModal(false);
                  setNewProject({ name: '', description: '' });
                }}
              >
                {t.cancel}
              </button>
              <button
                className="btn-primary"
                onClick={handleCreateProject}
                disabled={!newProject.name.trim()}
              >
                {t.create}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIAnalyzerPage;
