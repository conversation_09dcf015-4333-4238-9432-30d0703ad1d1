import { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import '../styles/AIProjectPage.css';
import { PRESET_SEARCH_PARAMS } from '../services/siteSelectionService';

const AIProjectPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [project, setProject] = useState(null);
  const [language, setLanguage] = useState(localStorage.getItem('preferredLanguage') || 'en');
  const [inputText, setInputText] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [error, setError] = useState(null);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [showResultsOnly, setShowResultsOnly] = useState(false);

  const dropdownRef = useRef(null);

  const supportedLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'zh', name: '中文', flag: '🇨🇳' }
  ];

  const translations = {
    en: {
      backToProjects: 'Back to Projects',
      projectNotFound: 'Project not found',
      inputPlaceholder: 'Enter information about the industrial park you want to analyze...\n\nFor example:\n- Location details (city, state, country)\n- Transportation access (highways, ports, airports)\n- Available utilities and infrastructure\n- Target industries or business requirements\n- Size and zoning information',
      analyzeButton: 'Analyze with AI',
      analyzing: 'Analyzing...',
      analysisResults: 'Analysis Results',
      analysisSummaryTitle: 'Analysis Summary',
      extractedInfo: 'Extracted Information',
      cities: 'Cities',
      countries: 'Countries',
      highways: 'Highways',
      industries: 'Industries',
      states_provinces: 'States/Provinces',
      facility_type: 'Facility Type',
      specific_requirements: 'Specific Requirements',
      airports: 'Airports',
      ports: 'Ports',
      property_info: 'Property Info',
      other: 'Other Keywords',
      noResults: 'No analysis results yet',
      error: 'Error',
      tryAgain: 'Try Again',
      clearResults: 'Clear Results',
      selectLanguage: 'Select Language',
      focusResults: 'Focus on Results',
      showBoth: 'Show Both Panels',
      editResults: 'Click to edit',
      addItem: 'Add Item',
      deleteItem: 'Delete',
      saveChanges: 'Save Changes',
      exportResults: 'Export Results',
      siteSelection: 'Site Selection',
      siteSelectionTitle: 'Industrial Site Selection',
      siteSelectionSubtitle: 'Find optimal industrial locations based on your requirements',
      runProspecting: 'Run Site Prospecting',
      prospectingResults: 'Prospecting Results',
      financialAnalysis: 'Financial Analysis',
      loading: 'Loading...',
      noSiteResults: 'No site selection results yet',
      parcelId: 'Parcel ID',
      totalCost: 'Total Cost',
      area: 'Area',
      location: 'Location',
      costBreakdown: 'Cost Breakdown',
      laborCost: 'Labor Cost',
      utilityCost: 'Utility Cost',
      transportCost: 'Transportation Cost',
      landCost: 'Land Cost',
      selectParcel: 'Select for Analysis',
      viewToggle: 'View',
      listView: 'List View',
      mapView: 'Map View',
      showOnMap: 'Show on Map',
      selectedParcel: 'Selected Parcel',
      irr: 'IRR',
      npv: 'NPV',
      cashOnCash: 'Cash-on-Cash',
      performanceRating: 'Performance Rating'
    },
    zh: {
      backToProjects: '返回项目列表',
      projectNotFound: '项目未找到',
      inputPlaceholder: '请输入您想要分析的工业园区信息...\n\n例如：\n- 位置详情（城市、州/省、国家）\n- 交通便利性（高速公路、港口、机场）\n- 可用设施和基础设施\n- 目标行业或业务需求\n- 规模和分区信息',
      analyzeButton: 'AI分析',
      analyzing: '分析中...',
      analysisResults: '分析结果',
      analysisSummaryTitle: '分析摘要',
      extractedInfo: '提取的信息',
      cities: '城市',
      countries: '国家',
      highways: '高速公路',
      industries: '行业',
      states_provinces: '州/省',
      facility_type: '设施类型',
      specific_requirements: '具体要求',
      airports: '机场',
      ports: '港口',
      property_info: '属性信息',
      other: '其他关键词',
      noResults: '暂无分析结果',
      error: '错误',
      tryAgain: '重试',
      clearResults: '清除结果',
      selectLanguage: '选择语言',
      focusResults: '专注结果',
      showBoth: '显示双面板',
      editResults: '点击编辑',
      addItem: '添加项目',
      deleteItem: '删除',
      saveChanges: '保存更改',
      exportResults: '导出结果',
      siteSelection: '站点选择',
      siteSelectionTitle: '工业站点选择',
      siteSelectionSubtitle: '根据您的需求找到最佳工业位置',
      runProspecting: '运行站点勘探',
      prospectingResults: '勘探结果',
      financialAnalysis: '财务分析',
      loading: '加载中...',
      noSiteResults: '暂无站点选择结果',
      parcelId: '地块ID',
      totalCost: '总成本',
      area: '面积',
      location: '位置',
      costBreakdown: '成本明细',
      laborCost: '劳动力成本',
      utilityCost: '公用事业成本',
      transportCost: '运输成本',
      landCost: '土地成本',
      selectParcel: '选择进行分析',
      viewToggle: '视图',
      listView: '列表视图',
      mapView: '地图视图',
      showOnMap: '在地图上显示',
      selectedParcel: '已选择地块',
      irr: '内部收益率',
      npv: '净现值',
      cashOnCash: '现金回报率',
      performanceRating: '性能评级'
    }
  };

  const t = translations[language] || translations.en;

  const getCurrentLanguage = () => {
    return supportedLanguages.find(lang => lang.code === language) || supportedLanguages[0];
  };

  useEffect(() => {
    document.documentElement.classList.add('ai-project-active');
    document.body.classList.add('ai-project-active');
    return () => {
      document.documentElement.classList.remove('ai-project-active');
      document.body.classList.remove('ai-project-active');
    };
  }, []);

  useEffect(() => {
    const loadProject = async () => {
      if (!id) return;
      try {
        const { authAPI, projectAPI } = await import('../services/api');
        if (!authAPI.isAuthenticated()) {
          navigate('/login');
          return;
        }
        const response = await projectAPI.getProject(id);
        if (response.success) {
          const formattedProject = {
            id: response.data.project.id.toString(),
            name: response.data.project.project_name,
            description: response.data.project.description || '',
            createdAt: response.data.project.created_at,
            updatedAt: response.data.project.updated_at,
            status: response.data.project.status,
            naturalLanguageInput: response.data.project.natural_language_input || '',
            structuredParameters: response.data.project.structured_parameters || '{}'
          };
          setProject(formattedProject);
        }
      } catch (error) {
        console.error('加载项目失败:', error);
        if (error.message.includes('401') || error.message.includes('403')) navigate('/login');
        else if (error.message.includes('404')) navigate('/ai-analyzer');
      }
    };
    loadProject();
  }, [id, navigate]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowLanguageDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLanguageChange = useCallback((newLang) => {
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
    setShowLanguageDropdown(false);
  }, []);

  const toggleLanguageDropdown = useCallback(() => {
    setShowLanguageDropdown(prev => !prev);
  }, []);

  const analyzeWithAI = useCallback(async () => {
    if (!inputText.trim()) return;

    setIsAnalyzing(true);
    setError(null);

    // =================================================================
    // V5.2 - PROMPT MODIFIED FOR MULTIPLE CITY SUGGESTIONS
    // =================================================================
    const prompt = `
# ROLE AND GOAL
You are a Principal Analyst at a top-tier global industrial real estate and supply chain consultancy. Your client (the user) has provided a query for a potential site or facility. Your goal is to function as an expert system that performs a rigorous, multi-layered analysis of the user's input, delivering a structured, professional, and insightful data object.

# PRIMARY DIRECTIVES

1.  **Internal Analysis First (Chain-of-Thought)**: Before generating the JSON, mentally deconstruct the user's query.
    -   Identify the core geography (cities, country, specific landmarks).
    -   List the explicit facility specifications (size, height, type).
    -   Recognize the stated business purpose (e.g., e-commerce, manufacturing).
    -   Pinpoint key infrastructure mentions (highways, ports).

2.  **Expert Inference and Enrichment**: This is your primary value. Go beyond literal extraction.
    -   **Connect Business Purpose to Needs**: If the user mentions "e-commerce," you *must* infer requirements like "last-mile delivery optimization," "high throughput sorting," and facility types like "fulfillment center." If they mention "biopharma," you *must* infer "GMP compliance," "clean rooms," "temperature control," etc.
    -   **Standardize and Formalize**: Normalize entities.
        - "near Hamburg port" should be analyzed to identify relevant city (e.g., \`cities: ["Hamburg"]\`) and \`ports: ["Port of Hamburg"]\`.
        - For highways, always extract the specific name or number. For example, "A7 autobahn" becomes \`highways: ["A7"]\`. "Access to Interstate 95" becomes \`highways: ["I-95"]\`. If the user mentions "near an interstate" or "good highway access" without specifying *which* highway, and it cannot be reasonably inferred from other location details, you can use a descriptive phrase like \`["Requires good highway access"]\` or an empty array \`[]\` if it's a passing mention without emphasis. Avoid inventing highway numbers or names.
    -   **Geographic Inference and City Identification**:
        - Your primary goal is to identify and list specific cities that match the user's requirements, even if not explicitly named. The output for cities should be in the \`location.cities\` array.
        - If the user explicitly mentions one or more cities, include them in the \`location.cities\` array.
        - If the user describes a region or provides contextual clues (e.g., "southern Germany near the Swiss border and a major cargo airport," "a logistics hub in the Benelux region with good port access"), analyze these clues to identify and list *multiple plausible cities* in the \`location.cities\` array. It is better to provide a few relevant city options than no cities if the requirements are somewhat broad but inferable.
        - Populate \`location.state_province\` and \`location.country\` based on the overarching geography of the query or the identified cities. If multiple states/provinces or countries are relevant, list them.
        - If, after analysis, no specific cities can be reasonably inferred or match the criteria, the \`location.cities\` array should be empty (\`[]\`). Do not invent cities.

3.  **Strict Schema Adherence**: The output MUST be a single, valid JSON object and nothing else. No introductory text, no explanations, no apologies. If a field is not applicable or information is missing, use an empty array \`[]\` or \`null\` for appropriate non-array string fields where allowed by schema (though prefer empty arrays for list-like string attributes).

4.  **Language Consistency**: The language used for the values within the JSON MUST match the primary language of the user's input text.

5.  **Final Validation**: Before outputting, double-check your generated JSON against the schema provided below to ensure 100% compliance.

---
# JSON OUTPUT SCHEMA

{
  "analysis_summary": string,
  "location": {
    "text_mention": string,
    "cities": string[],
    "state_province": string[] | null,
    "country": string[] | null,
    "proximity_notes": string[]
  },
  "facility": {
    "type": string[],
    "explicit_requirements": string[],
    "inferred_requirements": string[],
    "quantitative_info": string[]
  },
  "infrastructure": {
    "highways": string[],
    "airports": string[],
    "ports": string[]
  },
  "industry_tags": string[]
}

---
# EXAMPLES

## Example 1: (Rich, Detailed Input in Chinese)
* **Input**: "我正在寻找一个靠近上海港的，大约5000平米，净高9米的现代化仓库，用于存放进口的高价值电子产品。需要有24小时安保和温湿度控制。"
* **Output**:
    {
      "analysis_summary": "Requirement for a modern, climate-controlled warehouse near the Port of Shanghai for high-value electronics storage.",
      "location": {
        "text_mention": "靠近上海港",
        "cities": ["上海"],
        "state_province": ["上海"],
        "country": ["中国"],
        "proximity_notes": ["靠近上海港"]
      },
      "facility": {
        "type": ["现代化仓库", "高标仓", "配送中心"],
        "explicit_requirements": ["24小时安保", "温湿度控制", "用于存放进口的高价值电子产品"],
        "inferred_requirements": ["防静电措施", "高级别安防系统", "适用于高价值货物"],
        "quantitative_info": ["面积: 5000平米", "净高: 9米"]
      },
      "infrastructure": { "highways": [], "airports": [], "ports": ["上海港"] },
      "industry_tags": ["电子产品", "高科技", "供应链", "进口物流", "仓储"]
    }

## Example 2: (Standard Input with Enrichment in English)
* **Input**: "I need a logistics hub near Hamburg with good connection to the port and A7 autobahn, for e-commerce fulfillment."
* **Output**:
    {
      "analysis_summary": "Seeking an e-commerce logistics hub in the Hamburg area with direct access to the port and A7 highway.",
      "location": {
        "text_mention": "near Hamburg",
        "cities": ["Hamburg"],
        "state_province": ["Hamburg"],
        "country": ["Germany"],
        "proximity_notes": ["Good connection to the port", "Good connection to A7 autobahn"]
      },
      "facility": {
        "type": ["logistics hub", "fulfillment center", "e-commerce warehouse"],
        "explicit_requirements": ["for e-commerce fulfillment"],
        "inferred_requirements": ["high throughput sorting", "last-mile delivery optimization", "scalable storage"],
        "quantitative_info": []
      },
      "infrastructure": { "highways": ["A7"], "airports": [], "ports": ["Port of Hamburg"] },
      "industry_tags": ["e-commerce", "logistics", "last-mile delivery", "3PL", "distribution"]
    }

## Example 3: (Input leading to multiple city suggestions)
* **Input**: "We need options for a large e-commerce fulfillment center in the US Midwest, near a major cargo airport and with good interstate highway access. Focus on Ohio or Indiana."
* **Output**:
    {
      "analysis_summary": "Seeking site options for a large e-commerce fulfillment center in Ohio or Indiana, requiring proximity to a major cargo airport and interstate highways.",
      "location": {
        "text_mention": "US Midwest, near a major cargo airport and with good interstate highway access. Focus on Ohio or Indiana.",
        "cities": ["Indianapolis, IN", "Columbus, OH", "Cincinnati, OH"],
        "state_province": ["Indiana", "Ohio"],
        "country": ["USA"],
        "proximity_notes": ["Near a major cargo airport", "Good interstate highway access"]
      },
      "facility": {
        "type": ["e-commerce fulfillment center", "large warehouse", "logistics facility"],
        "explicit_requirements": ["Focus on Ohio or Indiana"],
        "inferred_requirements": ["high throughput sorting", "last-mile delivery capabilities", "scalable design"],
        "quantitative_info": []
      },
      "infrastructure": { "highways": ["Requires good interstate access"], "airports": ["Proximity to major cargo airport required"], "ports": [] },
      "industry_tags": ["e-commerce", "logistics", "fulfillment", "distribution", "Midwest USA"]
    }
---

# ANALYZE THE FOLLOWING USER TEXT:

**Input Text:** "${inputText}"

**JSON Output:**
`;

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${"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"}`, // 请务必替换为您的有效API密钥
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          'model': 'tngtech/deepseek-r1t-chimera:free', // 或您选择的其他模型
          'messages': [
            {
              'role': 'user',
              'content': prompt
            }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const aiResponse = data.choices[0].message.content;

      try {
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const extractedData = JSON.parse(jsonMatch[0]);
          console.log('AI Extracted Data:', extractedData); // 用于调试

          // **[MODIFIED]** Adjust to handle arrays for cities, states, countries
          const displayData = {
            summary: extractedData.analysis_summary || '',
            cities: extractedData.location?.cities || [],
            states_provinces: extractedData.location?.state_province || [],
            countries: extractedData.location?.country || [],
            highways: extractedData.infrastructure?.highways || [],
            ports: extractedData.infrastructure?.ports || [],
            airports: extractedData.infrastructure?.airports || [],
            facility_type: extractedData.facility?.type || [],
            specific_requirements: [
              ...(extractedData.facility?.explicit_requirements || []),
              ...(extractedData.facility?.inferred_requirements || [])
            ],
            property_info: extractedData.facility?.quantitative_info || [],
            industries: extractedData.industry_tags || [],
            other: extractedData.location?.proximity_notes || [],
          };
          
          setAnalysisResult(displayData);

        } else {
          throw new Error('No valid JSON found in AI response');
        }
      } catch (parseError) {
        console.error('JSON parsing error:', parseError);
        setError('Failed to parse AI response. Raw output logged.');
        setAnalysisResult({ other: [aiResponse] });
      }

    } catch (err) {
      console.error('AI Analysis error:', err);
      setError(err.message);
    } finally {
      setIsAnalyzing(false);
    }
  }, [inputText]);

  const clearResults = useCallback(() => {
    setAnalysisResult(null);
    setError(null);
    setShowResultsOnly(false);
  }, []);
  
  const toggleViewMode = useCallback(() => {
    setShowResultsOnly(prev => !prev);
  }, []);

  const addItemToCategory = useCallback((category) => {
    const newItem = prompt(language === 'en' ? 'Enter new item:' : '输入新项目:');
    if (newItem && newItem.trim()) {
      setAnalysisResult(prev => ({
        ...prev,
        [category]: [...(prev[category] || []), newItem.trim()]
      }));
    }
  }, [language]);

  const deleteItem = useCallback((category, index) => {
    setAnalysisResult(prev => ({
      ...prev,
      [category]: prev[category].filter((_, i) => i !== index)
    }));
  }, []);

  const exportResults = useCallback(() => {
    if (!analysisResult) return;
    const exportData = {
      projectName: project?.name || 'AI Analysis',
      timestamp: new Date().toISOString(),
      inputText,
      results: analysisResult
    };
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-analysis-${project?.name.replace(/\s/g, '_') || 'export'}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [analysisResult, project, inputText]);

  // 站点选择处理函数 - 导航到专业站点选择页面
  const handleSiteSelection = useCallback(() => {
    if (!analysisResult) {
      setError('请先完成AI分析');
      return;
    }

    // 导航到站点选择页面，传递分析结果和项目信息
    navigate('/site-selection', {
      state: {
        analysisResult,
        projectInfo: {
          id: id,
          name: project?.name || `Project ${id}`,
          description: project?.description || ''
        }
      }
    });
  }, [analysisResult, navigate, id, project]);



  if (!project) {
    return (
      <div className="ai-project-page">
        <div className="ai-background"><div className="grid-pattern"></div></div>
        <div className="error-container">
          <h2>{t.projectNotFound}</h2>
          <button className="back-button" onClick={() => navigate('/ai-analyzer')}>{t.backToProjects}</button>
        </div>
      </div>
    );
  }

  return (
    <div className="ai-project-page">
      <div className="ai-background">
        <div className="tech-grid"></div>
      </div>
      <header className="ai-header">
        <div className="header-container">
          <div className="header-left">
            <button className="back-button" onClick={() => navigate('/ai-analyzer')}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span>{t.backToProjects}</span>
            </button>
          </div>
          <div className="header-center">
            <h1 className="project-title">{project.name}</h1>
            <div className="project-status">
              <div className="status-indicator"></div>
              <span>{language === 'en' ? 'Active Analysis' : '活跃分析'}</span>
            </div>
          </div>
          <div className="header-right">
            <div className="language-selector" ref={dropdownRef}>
              <button className="language-trigger" onClick={toggleLanguageDropdown}>
                <span className="language-flag">{getCurrentLanguage().flag}</span>
                <span className="language-name">{getCurrentLanguage().name}</span>
                <svg className={`dropdown-icon ${showLanguageDropdown ? 'open' : ''}`} width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
              {showLanguageDropdown && (
                <div className="language-menu">
                  {supportedLanguages.map((lang) => (
                    <button key={lang.code} className={`language-option ${language === lang.code ? 'active' : ''}`} onClick={() => handleLanguageChange(lang.code)}>
                      <span className="language-flag">{lang.flag}</span>
                      <span className="language-name">{lang.name}</span>
                      {language === lang.code && (
                        <svg className="check-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </header>
      <main className="ai-main">
        <div className="ai-container">
          <div className={`content-layout ${showResultsOnly ? 'results-focused' : 'dual-panel'}`}>
            <div className={`input-panel ${showResultsOnly ? 'hidden' : ''}`}>
              <div className="panel-header">
                <div className="header-info">
                  <h2>{language === 'en' ? 'Analysis Input' : '分析输入'}</h2>
                  <p>{language === 'en' ? 'Describe your industrial requirements' : '描述您的工业需求'}</p>
                </div>
                <div className="input-meta">
                  <span className="char-count">{inputText.length}</span>
                </div>
              </div>
              <div className="input-content">
                <textarea
                  className="input-textarea"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  placeholder={t.inputPlaceholder}
                  rows={14}
                />
                <div className="quick-inputs">
                  {['Location', 'Transportation', 'Utilities', 'Industry'].map((chip, index) => (
                    <button key={index} className="quick-input-chip" onClick={() => {
                      const suggestions = { 0: 'Located in ', 1: 'Near highway ', 2: 'With utilities: ', 3: 'Suitable for industry: '};
                      setInputText(prev => prev + (language === 'zh' ? {0: '位于', 1: '靠近高速', 2: '拥有设施：', 3: '适用于行业：'}[index] : suggestions[index]));
                    }}>
                      {language === 'en' ? chip : {0: '位置', 1: '交通', 2: '设施', 3: '行业'}[index]}
                    </button>
                  ))}
                </div>
              </div>
              <div className="input-actions">
                <button className="analyze-button primary" onClick={analyzeWithAI} disabled={!inputText.trim() || isAnalyzing}>
                  {isAnalyzing ? (
                    <>
                      <div className="loading-spinner"></div>
                      <span>{t.analyzing}</span>
                    </>
                  ) : (
                    <>
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <span>{t.analyzeButton}</span>
                    </>
                  )}
                </button>
                {analysisResult && (
                  <button className="clear-button secondary" onClick={clearResults}>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>{t.clearResults}</span>
                  </button>
                )}
              </div>
            </div>
            <div className="results-panel">
              <div className="panel-header">
                <div className="header-info">
                  <h2>{t.analysisResults}</h2>
                  <p>{language === 'en' ? 'AI-powered analysis results' : 'AI智能分析结果'}</p>
                </div>
                {analysisResult && (
                  <div className="results-meta">
                    <span className="results-count">{Object.values(analysisResult).flat().length}</span>
                    <button className="view-toggle-button" onClick={() => setShowResultsOnly(!showResultsOnly)}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d={showResultsOnly ? "M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3" : "M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7"} stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <span>{showResultsOnly ? t.showBoth : t.focusResults}</span>
                    </button>
                  </div>
                )}
              </div>
              <div className="results-content">
                {error && (
                  <div className="error-state">
                    <div className="error-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                        <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/>
                        <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                    </div>
                    <div className="error-details">
                      <h4>{t.error}</h4>
                      <p>{error}</p>
                      <button className="retry-button" onClick={analyzeWithAI}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M23 4v6h-6M1 20v-6h6M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4a9 9 0 0 1-14.85 4.36L3 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        {t.tryAgain}
                      </button>
                    </div>
                  </div>
                )}
                {!analysisResult && !error && (
                  <div className="empty-state">
                    <div className="empty-icon">
                      <svg width="80" height="80" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" strokeDasharray="4 4" opacity="0.3"/>
                        <path d="M12 6v6l4 2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" opacity="0.5"/>
                      </svg>
                    </div>
                    <h3>{t.noResults}</h3>
                    <p>{language === 'en' ? 'Enter your requirements and click analyze to get started' : '输入您的需求并点击分析开始'}</p>
                  </div>
                )}
                {analysisResult && (
                  <div className="analysis-results">
                    {analysisResult.summary && (
                      <div className="summary-section">
                        <h4>{t.analysisSummaryTitle}</h4>
                        <p className="summary-text">{analysisResult.summary}</p>
                      </div>
                    )}
                    <div className="results-header">
                      <h3>{t.extractedInfo}</h3>
                      <div className="results-actions">
                        <button className="export-button" onClick={exportResults} title={t.exportResults}>
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </button>
                        <span className="edit-hint">{t.editResults}</span>
                      </div>
                    </div>
                    <div className="results-grid">
                      {Object.entries(analysisResult).map(([category, items]) => (
                        (items && items.length > 0 && category !== 'summary') && (
                        <div key={category} className="result-category">
                          <div className="category-header">
                            <div className="category-info">
                              <h4>{t[category] || category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                              <span className="item-count">{items?.length || 0}</span>
                            </div>
                            <button className="add-item-button" onClick={() => addItemToCategory(category)} title={t.addItem}>
                              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </button>
                          </div>
                          <div className="result-items">
                            {items.map((item, index) => (
                              <div key={index} className="result-item">
                                <span className="result-tag" contentEditable suppressContentEditableWarning={true} onBlur={(e) => {
                                  const newValue = e.target.textContent;
                                  if (newValue !== item && newValue.trim()) {
                                    const newItems = [...items]; newItems[index] = newValue.trim();
                                    setAnalysisResult(prev => ({...prev, [category]: newItems}));
                                  } else if (!newValue.trim()) { e.target.textContent = item; }
                                }} onKeyDown={(e) => { if (e.key === 'Enter') { e.preventDefault(); e.target.blur(); } }}>{item}</span>
                                <button className="delete-item-button" onClick={() => deleteItem(category, index)} title={t.deleteItem}>
                                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                                    <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  </svg>
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                        )
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            {analysisResult && (
              <div className="site-selection-section">
                <button className="site-selection-button" onClick={handleSiteSelection}>
                  <div className="button-content">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>{t.siteSelection}</span>
                  </div>
                </button>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default AIProjectPage;