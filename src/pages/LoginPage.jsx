import React, { useState, useRef, useEffect, useCallback, useMemo, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Canvas, useFrame } from '@react-three/fiber';
import { Stars, OrbitControls } from '@react-three/drei';
import * as THREE from 'three';
import '../styles/LoginPage.css';
import CompactErrorMessage from '../components/common/CompactErrorMessage';

// 优化的粒子背景组件 - 使用React.memo提高性能
const ParticleBackground = memo(() => {
  const pointsRef = useRef();
  const particleCount = 800; // 减少粒子数量以提高性能

  // 使用useMemo缓存粒子位置计算
  const positions = useMemo(() => {
    const pos = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount; i++) {
      pos[i * 3] = (Math.random() - 0.5) * 25;
      pos[i * 3 + 1] = (Math.random() - 0.5) * 25;
      pos[i * 3 + 2] = (Math.random() - 0.5) * 25;
    }
    return pos;
  }, [particleCount]);

  // 优化的动画循环，使用useCallback缓存函数 - 移除旋转以防止横向移动
  const animateParticles = useCallback(({ clock }) => {
    if (pointsRef.current) {
      const time = clock.getElapsedTime();
      // 只保留轻微的垂直浮动效果
      pointsRef.current.position.y = Math.sin(time * 0.5) * 0.1;
    }
  }, []);

  useFrame(animateParticles);

  return (
    <points ref={pointsRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.015}
        color="#4dc8ff"
        transparent
        opacity={0.7}
        sizeAttenuation={false}
      />
    </points>
  );
});

// 优化的3D背景场景组件
const Background3D = memo(() => {
  return (
    <Canvas
      camera={{ position: [0, 0, 5], fov: 75 }}
      style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
      gl={{ antialias: true, alpha: true }}
      dpr={[1, 2]} // 限制设备像素比以提高性能
    >
      <color attach="background" args={["#0a0f1c"]} />
      <ParticleBackground />
      <Stars
        radius={100}
        depth={50}
        count={1500} // 减少星星数量以提高性能
        factor={3}
        saturation={0}
        fade
        speed={0} // 移除星星移动以防止横向移动
      />
      <OrbitControls
        enableZoom={false}
        enablePan={false}
        enableRotate={false}
        autoRotate={false}
        autoRotateSpeed={0}
      />
    </Canvas>
  );
});

// 主登录页面组件 - 使用性能优化
const LoginPage = memo(() => {
  const navigate = useNavigate();
  const [isLogin, setIsLogin] = useState(true);
  const [language, setLanguage] = useState(localStorage.getItem('preferredLanguage') || 'en');
  const [formData, setFormData] = useState({
    loginIdentifier: '', // 支持邮箱或用户名
    password: '',
    confirmPassword: '',
    username: '',
    email: '',
    preferredLanguage: language // 添加语言偏好字段
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState('');
  const [formProgress, setFormProgress] = useState(0);
  const [rememberMe, setRememberMe] = useState(false);
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
  const [errorType, setErrorType] = useState('error'); // 'error', 'warning', 'info', 'critical'
  const [isDismissing, setIsDismissing] = useState(false);

  // 使用useMemo缓存计算结果
  const isEnglish = useMemo(() => language === 'en', [language]);

  // 使用useMemo缓存文本内容
  const texts = useMemo(() => ({
    en: {
      welcome: 'Welcome to',
      appName: 'Industrial Geo Explorer',
      subtitle: 'Advanced Geographic Intelligence Platform',
      loginTitle: 'Sign In',
      registerTitle: 'Create Account',
      loginIdentifier: 'Email or Username',
      email: 'Email Address',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      username: 'Username',
      loginButton: 'Sign In',
      registerButton: 'Create Account',
      switchToRegister: "Don't have an account? Sign up",
      switchToLogin: 'Already have an account? Sign in',
      backToHome: 'Back to Home',
      forgotPassword: 'Forgot Password?',
      rememberMe: 'Remember me',
      orDivider: 'OR',
      googleLogin: 'Continue with Google',
      githubLogin: 'Continue with GitHub',
      loading: 'Please wait...',
      loginSubtitle: 'Welcome back to the platform',
      registerSubtitle: 'Join our advanced analytics platform',
      preferredLanguage: 'Preferred Language',
      selectLanguage: 'Select Language',
      english: 'English',
      chinese: 'Chinese',
      // Error messages
      loginFailed: 'Login failed, please check your credentials',
      registerFailed: 'Registration failed, please try again',
      systemError: 'System error: Unable to load authentication service',
      databaseError: 'Database connection error, please try again later',
      serverError: 'Server error, please try again later',
      networkError: 'Network error, please check your connection',
      invalidCredentials: 'Invalid email/username or password',
      emailExists: 'This email is already registered',
      usernameExists: 'This username is already taken',
      tryAgain: 'Please try again'
    },
    zh: {
      welcome: '欢迎使用',
      appName: '工域探索',
      subtitle: '先进地理智能平台',
      loginTitle: '登录',
      registerTitle: '注册账户',
      loginIdentifier: '邮箱或用户名',
      email: '邮箱地址',
      password: '密码',
      confirmPassword: '确认密码',
      username: '用户名',
      loginButton: '登录',
      registerButton: '注册',
      switchToRegister: '没有账户？立即注册',
      switchToLogin: '已有账户？立即登录',
      backToHome: '返回首页',
      forgotPassword: '忘记密码？',
      rememberMe: '记住我',
      orDivider: '或',
      googleLogin: '使用 Google 登录',
      githubLogin: '使用 GitHub 登录',
      loading: '请稍候...',
      loginSubtitle: '欢迎回到平台',
      registerSubtitle: '加入我们的高级分析平台',
      preferredLanguage: '首选语言',
      selectLanguage: '选择语言',
      english: '英语',
      chinese: '中文',
      // Error messages
      loginFailed: '登录失败，请检查您的凭据',
      registerFailed: '注册失败，请重试',
      systemError: '系统错误：无法加载认证服务',
      databaseError: '数据库连接错误，请稍后重试',
      serverError: '服务器错误，请稍后重试',
      networkError: '网络错误，请检查您的连接',
      invalidCredentials: '邮箱/用户名或密码错误',
      emailExists: '该邮箱已被注册',
      usernameExists: '该用户名已被使用',
      tryAgain: '请重试'
    }
  }), []);

  const t = useMemo(() => texts[language], [texts, language]);

  // 使用useCallback缓存密码强度检测函数
  const checkPasswordStrength = useCallback((password) => {
    if (!password) return '';

    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    if (score < 3) return 'weak';
    if (score < 5) return 'medium';
    return 'strong';
  }, []);

  // 使用useCallback缓存表单进度计算函数
  const calculateFormProgress = useCallback(() => {
    let progress = 0;
    const totalFields = isLogin ? 2 : 4;

    if (isLogin) {
      if (formData.loginIdentifier) progress++;
      if (formData.password) progress++;
    } else {
      if (formData.username) progress++;
      if (formData.email) progress++;
      if (formData.password) progress++;
      if (formData.confirmPassword) progress++;
    }

    return (progress / totalFields) * 100;
  }, [formData, isLogin]);

  // 使用useCallback缓存表单验证函数
  const validateForm = useCallback(() => {
    const newErrors = {};

    if (isLogin) {
      // 登录验证 - 支持邮箱或用户名
      if (!formData.loginIdentifier) {
        newErrors.loginIdentifier = isEnglish ? 'Email or username is required' : '邮箱或用户名是必填项';
      } else if (formData.loginIdentifier.includes('@') && !/\S+@\S+\.\S+/.test(formData.loginIdentifier)) {
        newErrors.loginIdentifier = isEnglish ? 'Email format is invalid' : '邮箱格式无效';
      }
    } else {
      // 注册验证
      if (!formData.username) {
        newErrors.username = isEnglish ? 'Username is required' : '用户名是必填项';
      } else if (formData.username.length < 3) {
        newErrors.username = isEnglish ? 'Username must be at least 3 characters' : '用户名至少需要3个字符';
      }

      if (!formData.email) {
        newErrors.email = isEnglish ? 'Email is required' : '邮箱是必填项';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = isEnglish ? 'Email is invalid' : '邮箱格式无效';
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = isEnglish ? 'Please confirm your password' : '请确认密码';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = isEnglish ? 'Passwords do not match' : '密码不匹配';
      }
    }

    // 密码验证（登录和注册都需要）
    if (!formData.password) {
      newErrors.password = isEnglish ? 'Password is required' : '密码是必填项';
    } else if (formData.password.length < 6) {
      newErrors.password = isEnglish ? 'Password must be at least 6 characters' : '密码至少需要6个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, isLogin, isEnglish]);

  // 使用useCallback缓存表单输入处理函数
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 检测密码强度
    if (name === 'password' && !isLogin) {
      setPasswordStrength(checkPasswordStrength(value));
    }

    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  }, [isLogin, checkPasswordStrength, errors]);

  // 更新表单进度
  useEffect(() => {
    setFormProgress(calculateFormProgress());
  }, [calculateFormProgress]);

  // 使用useCallback缓存表单提交处理函数
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    // 验证表单
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // 使用动态导入加载API模块
      const { authAPI } = await import('../services/api').catch(err => {
        console.error('无法加载API模块:', err);
        throw new Error(isEnglish ? 'System error: Unable to load authentication service' : '系统错误: 无法加载认证服务');
      });

      console.log('准备发送认证请求...', isLogin ? '登录' : '注册');

      if (isLogin) {
        // 登录 - 支持邮箱或用户名
        const response = await authAPI.login({
          loginIdentifier: formData.loginIdentifier,
          password: formData.password
        });

        console.log('登录响应:', response);

        if (response.success) {
          // 保存记住我设置
          if (rememberMe) {
            localStorage.setItem('rememberLogin', 'true');
          }

          // 登录成功后，应用用户的语言偏好
          if (response.data && response.data.user && response.data.user.preferred_language) {
            localStorage.setItem('preferredLanguage', response.data.user.preferred_language);
            setLanguage(response.data.user.preferred_language);
          }

          console.log('登录成功，准备导航到首页');
          navigate('/');
        } else {
          throw new Error(response.message || (isEnglish ? 'Login failed, please check your credentials' : '登录失败，请检查您的凭据'));
        }
      } else {
        // 注册
        const response = await authAPI.register({
          email: formData.email,
          password: formData.password,
          username: formData.username,
          preferred_language: formData.preferredLanguage
        });

        console.log('注册响应:', response);

        if (response.success) {
          // 注册成功后，设置用户选择的语言偏好
          if (response.data && response.data.user && response.data.user.preferred_language) {
            localStorage.setItem('preferredLanguage', response.data.user.preferred_language);
            setLanguage(response.data.user.preferred_language);
          }
          console.log('注册成功，准备导航到首页');
          navigate('/');
        } else {
          throw new Error(response.message || (isEnglish ? 'Registration failed, please try again' : '注册失败，请重试'));
        }
      }
    } catch (error) {
      console.error('认证错误:', error);

      // Categorize error and provide appropriate message
      let errorMessage = '';

      if (error.message) {
        const errorMsg = error.message.toLowerCase();

        // Database/Server errors
        if (errorMsg.includes('table') && errorMsg.includes('exist')) {
          errorMessage = t.databaseError;
        } else if (errorMsg.includes('database') || errorMsg.includes('connection')) {
          errorMessage = t.databaseError;
        } else if (errorMsg.includes('server') || errorMsg.includes('internal')) {
          errorMessage = t.serverError;
        } else if (errorMsg.includes('network') || errorMsg.includes('fetch')) {
          errorMessage = t.networkError;
        } else if (errorMsg.includes('system error') || errorMsg.includes('unable to load')) {
          errorMessage = t.systemError;
        } else if (errorMsg.includes('邮箱已被注册') || errorMsg.includes('email') && errorMsg.includes('registered')) {
          errorMessage = t.emailExists;
        } else if (errorMsg.includes('用户名已被使用') || errorMsg.includes('username') && errorMsg.includes('taken')) {
          errorMessage = t.usernameExists;
        } else if (errorMsg.includes('密码错误') || errorMsg.includes('invalid') || errorMsg.includes('credentials')) {
          errorMessage = t.invalidCredentials;
        } else {
          // Use the original error message if it's already translated
          errorMessage = error.message;
        }
      } else {
        // Fallback error messages
        errorMessage = isLogin ? t.loginFailed : t.registerFailed;
      }

      // Determine error type based on error message
      let errorClass = 'error';
      const errorMsgLower = errorMessage.toLowerCase();
      if (errorMsgLower.includes('database') || errorMsgLower.includes('server') || errorMsgLower.includes('system')) {
        errorClass = 'error-critical';
        setErrorType('critical');
      } else if (errorMsgLower.includes('network') || errorMsgLower.includes('connection')) {
        errorClass = 'error-warning';
        setErrorType('warning');
      } else {
        setErrorType('error');
      }

      setErrors({
        submit: errorMessage,
        errorClass: errorClass
      });
    } finally {
      setIsLoading(false);
    }
  }, [validateForm, isLogin, formData, language, rememberMe, navigate, isEnglish, setErrorType]);

  // 使用useCallback缓存模式切换函数
  const toggleMode = useCallback(() => {
    setIsLogin(!isLogin);
    setErrors({});
    setPasswordStrength('');
    setFormData({
      loginIdentifier: '',
      password: '',
      confirmPassword: '',
      username: '',
      email: '',
      preferredLanguage: language
    });
  }, [isLogin, language]);

  // 使用useCallback缓存语言切换函数
  const toggleLanguage = useCallback(() => {
    const newLang = language === 'en' ? 'zh' : 'en';
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
    setFormData(prev => ({ ...prev, preferredLanguage: newLang }));
  }, [language]);

  // 使用useCallback缓存密码可见性切换函数
  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(!showPassword);
  }, [showPassword]);

  const toggleConfirmPasswordVisibility = useCallback(() => {
    setShowConfirmPassword(!showConfirmPassword);
  }, [showConfirmPassword]);

  // 语言下拉菜单处理函数
  const handleLanguageDropdownToggle = useCallback(() => {
    setIsLanguageDropdownOpen(!isLanguageDropdownOpen);
  }, [isLanguageDropdownOpen]);

  const handleLanguageSelect = useCallback((selectedLang) => {
    setLanguage(selectedLang);
    localStorage.setItem('preferredLanguage', selectedLang);
    setFormData(prev => ({ ...prev, preferredLanguage: selectedLang }));
    setIsLanguageDropdownOpen(false);
  }, []);

  const handleFormLanguageSelect = useCallback((selectedLang) => {
    setFormData(prev => ({ ...prev, preferredLanguage: selectedLang }));
  }, []);

  // 错误消息关闭处理函数
  const handleErrorDismiss = useCallback(() => {
    setIsDismissing(true);
    setTimeout(() => {
      setErrors(prev => ({ ...prev, submit: '', errorClass: '' }));
      setIsDismissing(false);
    }, 300); // 匹配CSS动画时间
  }, []);

  // 点击外部关闭语言下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isLanguageDropdownOpen && !event.target.closest('.floating-language-dropdown')) {
        setIsLanguageDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isLanguageDropdownOpen]);

  return (
    <div className="login-page" data-lang={language}>
      {/* 3D背景 */}
      <div className="login-background">
        <Background3D />
      </div>

      {/* 浮动导航按钮 */}
      <div className="floating-nav-buttons">
        {/* 左侧返回按钮 */}
        <button
          className="floating-back-button"
          onClick={() => navigate('/')}
        >
          <span className="back-icon">←</span>
          {t.backToHome}
        </button>

        {/* 右侧语言下拉菜单 */}
        <div className="floating-language-dropdown">
          <button
            className="floating-language-toggle"
            onClick={handleLanguageDropdownToggle}
          >
            <span className="language-icon">🌐</span>
            {isEnglish ? 'EN' : '中文'}
            <span className={`dropdown-arrow ${isLanguageDropdownOpen ? 'open' : ''}`}>▼</span>
          </button>

          {isLanguageDropdownOpen && (
            <div className="language-dropdown-menu">
              <button
                className={`language-option ${language === 'en' ? 'active' : ''}`}
                onClick={() => handleLanguageSelect('en')}
              >
                <span className="flag-icon">🇺🇸</span>
                English
              </button>
              <button
                className={`language-option ${language === 'zh' ? 'active' : ''}`}
                onClick={() => handleLanguageSelect('zh')}
              >
                <span className="flag-icon">🇨🇳</span>
                中文
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="login-container">
        <div className="login-content">
          {/* 左侧品牌区域 */}
          <div className="brand-section">
            <div className="brand-content">
              <h1 className="brand-title">
                <span className="welcome-text">{t.welcome}</span>
                <span className="app-name">{t.appName}</span>
              </h1>
              <p className="brand-subtitle">{t.subtitle}</p>

              {/* 装饰性元素 */}
              <div className="brand-decoration">
                <div className="tech-lines">
                  <div className="tech-line"></div>
                  <div className="tech-line"></div>
                  <div className="tech-line"></div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧表单区域 */}
          <div className="form-section">
            <div className="form-container">
              {/* 表单进度指示器 */}
              <div className="form-progress">
                <div
                  className="form-progress-bar"
                  style={{ width: `${formProgress}%` }}
                ></div>
              </div>

              <div className="form-header">
                <h2 className="form-title">
                  {isLogin ? t.loginTitle : t.registerTitle}
                </h2>
                <p className="form-subtitle">
                  {isLogin
                    ? (isEnglish ? 'Welcome back to the platform' : '欢迎回到平台')
                    : (isEnglish ? 'Join our advanced analytics platform' : '加入我们的高级分析平台')
                  }
                </p>
              </div>

              <form className="login-form" onSubmit={handleSubmit}>
                {/* 登录表单 */}
                {isLogin ? (
                  <>
                    <div className="form-group">
                      <label htmlFor="loginIdentifier">{t.loginIdentifier}</label>
                      <div className="input-wrapper">
                        <input
                          type="text"
                          id="loginIdentifier"
                          name="loginIdentifier"
                          value={formData.loginIdentifier}
                          onChange={handleInputChange}
                          required
                          placeholder={t.loginIdentifier}
                          className={errors.loginIdentifier ? 'error' : ''}
                          autoComplete="username"
                        />
                        <div className="input-icon">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      </div>
                      {errors.loginIdentifier && (
                        <div className="error-message field-error">
                          {errors.loginIdentifier}
                        </div>
                      )}
                    </div>

                    <div className="form-group">
                      <label htmlFor="password">{t.password}</label>
                      <div className="input-wrapper">
                        <input
                          type={showPassword ? "text" : "password"}
                          id="password"
                          name="password"
                          value={formData.password}
                          onChange={handleInputChange}
                          required
                          placeholder={t.password}
                          className={errors.password ? 'error' : ''}
                          autoComplete="current-password"
                        />
                        <button
                          type="button"
                          className="password-toggle"
                          onClick={togglePasswordVisibility}
                          aria-label={showPassword ? "Hide password" : "Show password"}
                        >
                          {showPassword ? (
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.02643 7.65663 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1752 15.0074 10.8016 14.8565C10.4281 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5719 9.14351 13.1984C8.99262 12.8248 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M1 1L23 23" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          ) : (
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          )}
                        </button>
                      </div>
                      {errors.password && (
                        <div className="error-message field-error">
                          {errors.password}
                        </div>
                      )}
                    </div>

                    <div className="form-options">
                      <label className="checkbox-label">
                        <input
                          type="checkbox"
                          checked={rememberMe}
                          onChange={(e) => setRememberMe(e.target.checked)}
                        />
                        <span className="checkmark"></span>
                        {t.rememberMe}
                      </label>
                      <button type="button" className="forgot-password">{t.forgotPassword}</button>
                    </div>
                  </>
                ) : (
                  /* 注册表单 */
                  <>
                    <div className="form-group">
                      <label htmlFor="username">{t.username}</label>
                      <div className="input-wrapper">
                        <input
                          type="text"
                          id="username"
                          name="username"
                          value={formData.username}
                          onChange={handleInputChange}
                          required
                          placeholder={t.username}
                          className={errors.username ? 'error' : ''}
                          autoComplete="username"
                        />
                        <div className="input-icon">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      </div>
                      {errors.username && (
                        <div className="error-message field-error">
                          {errors.username}
                        </div>
                      )}
                    </div>

                    <div className="form-group">
                      <label htmlFor="email">{t.email}</label>
                      <div className="input-wrapper">
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          placeholder={t.email}
                          className={errors.email ? 'error' : ''}
                          autoComplete="email"
                        />
                        <div className="input-icon">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <polyline points="22,6 12,13 2,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      </div>
                      {errors.email && (
                        <div className="error-message field-error">
                          {errors.email}
                        </div>
                      )}
                    </div>

                    <div className="form-group">
                      <label htmlFor="password">{t.password}</label>
                      <div className="input-wrapper">
                        <input
                          type={showPassword ? "text" : "password"}
                          id="password"
                          name="password"
                          value={formData.password}
                          onChange={handleInputChange}
                          required
                          placeholder={t.password}
                          className={errors.password ? 'error' : ''}
                          autoComplete="new-password"
                        />
                        <button
                          type="button"
                          className="password-toggle"
                          onClick={togglePasswordVisibility}
                          aria-label={showPassword ? "Hide password" : "Show password"}
                        >
                          {showPassword ? (
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.02643 7.65663 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1752 15.0074 10.8016 14.8565C10.4281 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5719 9.14351 13.1984C8.99262 12.8248 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M1 1L23 23" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          ) : (
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          )}
                        </button>
                      </div>
                      {errors.password && <div className="error-message">{errors.password}</div>}

                      {/* 密码强度指示器 */}
                      {formData.password && (
                        <div className={`password-strength ${passwordStrength}`}>
                          <div className="password-strength-bar"></div>
                          <span className="password-strength-text">
                            {passwordStrength === 'weak' && (isEnglish ? 'Weak' : '弱')}
                            {passwordStrength === 'medium' && (isEnglish ? 'Medium' : '中等')}
                            {passwordStrength === 'strong' && (isEnglish ? 'Strong' : '强')}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="form-group">
                      <label htmlFor="confirmPassword">{t.confirmPassword}</label>
                      <div className="input-wrapper">
                        <input
                          type={showConfirmPassword ? "text" : "password"}
                          id="confirmPassword"
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleInputChange}
                          required
                          placeholder={t.confirmPassword}
                          className={errors.confirmPassword ? 'error' : ''}
                          autoComplete="new-password"
                        />
                        <button
                          type="button"
                          className="password-toggle"
                          onClick={toggleConfirmPasswordVisibility}
                          aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                        >
                          {showConfirmPassword ? (
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.02643 7.65663 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1752 15.0074 10.8016 14.8565C10.4281 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5719 9.14351 13.1984C8.99262 12.8248 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M1 1L23 23" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          ) : (
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          )}
                        </button>
                      </div>
                      {errors.confirmPassword && (
                        <div className="error-message field-error">
                          {errors.confirmPassword}
                        </div>
                      )}
                    </div>

                    {/* 语言偏好选择 */}
                    <div className="form-group">
                      <label htmlFor="preferredLanguage">{t.preferredLanguage}</label>
                      <div className="input-wrapper">
                        <select
                          id="preferredLanguage"
                          name="preferredLanguage"
                          value={formData.preferredLanguage}
                          onChange={(e) => handleFormLanguageSelect(e.target.value)}
                          className="language-select"
                        >
                          <option value="en">{t.english}</option>
                          <option value="zh">{t.chinese}</option>
                        </select>
                        <div className="input-icon">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12.87 15.07L10.33 12.56L10.36 12.53C12.1 10.59 13.34 8.36 14.07 6H17V4H10V2H8V4H1V6H12.17C11.5 7.92 10.44 9.75 9 11.35C8.07 10.32 7.3 9.19 6.69 8H4.69C5.42 9.63 6.42 11.17 7.67 12.56L2.58 17.58L4 19L9 14L12.11 17.11L12.87 15.07ZM18.5 10H16.5L12 22H14L15.12 19H19.87L21 22H23L18.5 10ZM15.88 17L17.5 12.67L19.12 17H15.88Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      </div>
                      {errors.preferredLanguage && <div className="error-message">{errors.preferredLanguage}</div>}
                    </div>
                  </>
                )}

                {/* 显示提交错误信息 - 新的紧凑设计 */}
                {errors.submit && (
                  <CompactErrorMessage
                    message={errors.submit}
                    type={errorType}
                    compact={true}
                    className="login-form-error"
                  />
                )}

                <button
                  type="submit"
                  className="submit-button"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="loading-container">
                      <div className="loading-spinner"></div>
                      <span>{t.loading}</span>
                    </div>
                  ) : (
                    <>
                      <span>{isLogin ? t.loginButton : t.registerButton}</span>
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </>
                  )}
                </button>

                <div className="form-divider">
                  <div className="divider-line"></div>
                  <span className="divider-text">{t.orDivider}</span>
                  <div className="divider-line"></div>
                </div>

                <div className="social-login">
                  <button type="button" className="social-button google">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M22.56 12.25C22.56 11.47 22.49 10.72 22.36 10H12V14.26H17.92C17.66 15.63 16.88 16.79 15.71 17.57V20.34H19.28C21.36 18.42 22.56 15.6 22.56 12.25Z" fill="#4285F4"/>
                      <path d="M12 23C15.24 23 17.95 21.92 19.28 20.34L15.71 17.57C14.74 18.22 13.48 18.62 12 18.62C8.91 18.62 6.26 16.67 5.4 13.97H1.72V16.84C3.04 19.47 7.26 23 12 23Z" fill="#34A853"/>
                      <path d="M5.4 13.97C5.18 13.32 5.06 12.62 5.06 11.9C5.06 11.18 5.18 10.48 5.4 9.83V6.96H1.72C0.99 8.42 0.6 10.11 0.6 11.9C0.6 13.69 0.99 15.38 1.72 16.84L5.4 13.97Z" fill="#FBBC05"/>
                      <path d="M12 5.38C13.62 5.38 15.06 5.94 16.21 7.02L19.36 3.87C17.95 2.61 15.24 1.9 12 1.9C7.26 1.9 3.04 5.43 1.72 8.06L5.4 10.93C6.26 8.23 8.91 6.28 12 6.28V5.38Z" fill="#EA4335"/>
                    </svg>
                    {t.googleLogin}
                  </button>
                  <button type="button" className="social-button github">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 0C5.374 0 0 5.373 0 12C0 17.302 3.438 21.8 8.207 23.387C8.806 23.498 9 23.126 9 22.81V20.576C5.662 21.302 4.967 19.16 4.967 19.16C4.421 17.773 3.634 17.404 3.634 17.404C2.545 16.659 3.717 16.675 3.717 16.675C4.922 16.759 5.556 17.912 5.556 17.912C6.626 19.746 8.363 19.216 9.048 18.909C9.155 18.134 9.466 17.604 9.81 17.305C7.145 17 4.343 15.971 4.343 11.374C4.343 10.063 4.812 8.993 5.579 8.153C5.455 7.85 5.044 6.629 5.696 4.977C5.696 4.977 6.704 4.655 8.997 6.207C9.954 5.941 10.98 5.808 12 5.803C13.02 5.808 14.047 5.941 15.006 6.207C17.297 4.655 18.303 4.977 18.303 4.977C18.956 6.63 18.545 7.851 18.421 8.153C19.191 8.993 19.656 10.064 19.656 11.374C19.656 15.983 16.849 16.998 14.177 17.295C14.607 17.667 15 18.397 15 19.517V22.81C15 23.129 15.192 23.504 15.801 23.386C20.566 21.797 24 17.3 24 12C24 5.373 18.627 0 12 0Z" fill="currentColor"/>
                    </svg>
                    {t.githubLogin}
                  </button>
                </div>

                <div className="form-switch">
                  <button
                    type="button"
                    className="switch-button"
                    onClick={toggleMode}
                  >
                    {isLogin ? t.switchToRegister : t.switchToLogin}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

// 设置显示名称以便调试
LoginPage.displayName = 'LoginPage';

export default LoginPage;
