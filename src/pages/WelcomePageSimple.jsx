import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { authAPI } from '../services/api';

// 简化版本的WelcomePage用于调试
const WelcomePageSimple = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [error, setError] = useState(null);
  
  // 获取当前语言设置
  const currentLang = useMemo(() => localStorage.getItem('preferredLanguage') || 'en', []);
  const isEnglish = useMemo(() => currentLang === 'en', [currentLang]);

  // 检查用户登录状态
  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (authAPI.isAuthenticated()) {
          const currentUser = authAPI.getUser();
          setUser(currentUser);
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // 处理进入应用
  const handleEnterApp = useCallback(() => {
    navigate('/main');
  }, [navigate]);

  // 处理登录
  const handleLoginClick = useCallback(() => {
    navigate('/login');
  }, [navigate]);

  // 处理登出
  const handleLogout = useCallback(async () => {
    try {
      await authAPI.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }, []);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
        color: 'white',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '3px solid #00a8ff',
            borderTop: '3px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px'
          }}></div>
          <p>{isEnglish ? 'Loading...' : '加载中...'}</p>
        </div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
        color: 'white',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <h2 style={{ color: '#ff4757', marginBottom: '20px' }}>
            {isEnglish ? 'Error' : '错误'}
          </h2>
          <p style={{ marginBottom: '20px' }}>{error}</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              background: '#00a8ff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            {isEnglish ? 'Retry' : '重试'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* 导航栏 */}
      <nav style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        padding: '20px',
        background: 'rgba(0, 0, 0, 0.8)',
        backdropFilter: 'blur(10px)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          <h1 style={{ margin: 0, fontSize: '24px', color: '#00a8ff' }}>
            {isEnglish ? 'Industrial GeoAnalytics' : '工域探索'}
          </h1>
          
          <div>
            {user ? (
              <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
                <span>{isEnglish ? `Welcome, ${user.username}` : `欢迎，${user.username}`}</span>
                <button
                  onClick={handleLogout}
                  style={{
                    padding: '8px 16px',
                    background: 'transparent',
                    color: 'white',
                    border: '1px solid #00a8ff',
                    borderRadius: '5px',
                    cursor: 'pointer'
                  }}
                >
                  {isEnglish ? 'Logout' : '登出'}
                </button>
              </div>
            ) : (
              <button
                onClick={handleLoginClick}
                style={{
                  padding: '8px 16px',
                  background: '#00a8ff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '5px',
                  cursor: 'pointer'
                }}
              >
                {isEnglish ? 'LOGIN / REGISTER' : '登录/注册'}
              </button>
            )}
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main style={{
        paddingTop: '100px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        textAlign: 'center',
        padding: '20px'
      }}>
        <h1 style={{
          fontSize: '48px',
          marginBottom: '20px',
          background: 'linear-gradient(45deg, #00a8ff, #0078ff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          {isEnglish ? 'Industrial Geography Analytics Platform' : '工业地理分析平台'}
        </h1>
        
        <p style={{
          fontSize: '18px',
          marginBottom: '40px',
          maxWidth: '600px',
          lineHeight: '1.6'
        }}>
          {isEnglish 
            ? 'Explore industrial parks, analyze economic data, and discover geographic insights with our advanced analytics platform.'
            : '探索工业园区，分析经济数据，通过我们的先进分析平台发现地理洞察。'
          }
        </p>

        <button
          onClick={handleEnterApp}
          style={{
            padding: '15px 30px',
            fontSize: '18px',
            background: 'linear-gradient(45deg, #00a8ff, #0078ff)',
            color: 'white',
            border: 'none',
            borderRadius: '10px',
            cursor: 'pointer',
            transition: 'transform 0.3s ease',
            boxShadow: '0 4px 15px rgba(0, 168, 255, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.target.style.transform = 'translateY(-2px)';
          }}
          onMouseLeave={(e) => {
            e.target.style.transform = 'translateY(0)';
          }}
        >
          {isEnglish ? 'EXPLORE PLATFORM' : '探索平台'}
        </button>

        {/* 数据库连接状态 */}
        <div style={{
          marginTop: '40px',
          padding: '20px',
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '10px',
          backdropFilter: 'blur(10px)'
        }}>
          <h3>{isEnglish ? 'System Status' : '系统状态'}</h3>
          <p style={{ color: '#00ff88' }}>
            {isEnglish ? '✅ Database Connected' : '✅ 数据库已连接'}
          </p>
          <p style={{ color: '#00ff88' }}>
            {isEnglish ? '✅ Backend API Running' : '✅ 后端API运行中'}
          </p>
        </div>
      </main>
    </div>
  );
};

export default WelcomePageSimple;
