/* Reset and base styles for the main page */
html, body, #root, .app-container {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Ensure the map container takes full viewport height */
.map-page-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

/* Ensure the map takes up all available space */
.leaflet-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Enhanced Map Controls */
.enhanced-map-controls {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: none; /* Allow clicks to pass through to the map */
}

/* Left controls */
.enhanced-map-controls .left-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: auto;
}

/* Right controls */
.enhanced-map-controls .right-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 1001;
  pointer-events: auto;
}

/* Ensure buttons are visible and properly styled */
.enhanced-map-controls .right-controls .map-button {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.enhanced-map-controls .right-controls .map-button:hover {
  background-color: #f5f5f5;
}

.enhanced-map-controls .right-controls .map-button.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

/* Dark mode styles */
.dark-mode .enhanced-map-controls .right-controls .map-button {
  background-color: #1f1f1f;
  border-color: #333;
  color: #f0f0f0;
}

.dark-mode .enhanced-map-controls .right-controls .map-button:hover {
  background-color: #2a2a2a;
}

.dark-mode .enhanced-map-controls .right-controls .map-button.active {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* Fix for sidebar scrolling */
.sidebar {
  height: 100vh;
  overflow-y: auto;
  position: relative;
  z-index: 2;
}

/* Map controls container */
.leaflet-top.leaflet-left {
  top: 15px;
  left: 15px;
  z-index: 1000;
}

/* Map zoom controls */
.leaflet-control-zoom {
  border: 2px solid rgba(0,0,0,0.2);
  background-clip: padding-box;
  margin: 0 !important;
  box-shadow: 0 1px 5px rgba(0,0,0,0.4);
  border-radius: 4px;
  background: white;
}

.leaflet-control-zoom a {
  width: 30px;
  height: 30px;
  line-height: 30px;
  font-size: 22px;
  border-bottom: 1px solid #ccc;
}

.leaflet-control-zoom a:last-child {
  border-bottom: none;
}

/* Ensure no margin or padding on the body that could affect layout */
body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Fix for any potential scrollbar issues */
* {
  box-sizing: border-box;
}
